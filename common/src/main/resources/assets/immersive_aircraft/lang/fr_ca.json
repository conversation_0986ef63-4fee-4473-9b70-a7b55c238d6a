{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Avion immersif", "key.immersive_aircraft.multi_control_left": "G<PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "En avant", "key.immersive_aircraft.multi_control_backward": "Retour en arrière", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON> de la page", "key.immersive_aircraft.multi_control_down": "En bas", "key.immersive_aircraft.multi_control_pull": "Contrôleur de traction", "key.immersive_aircraft.multi_control_push": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "key.immersive_aircraft.multi_use": "Utiliser l'arme/la monture", "key.immersive_aircraft.fallback_control_left": "G<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "En avant", "key.immersive_aircraft.fallback_control_backward": "Retour en arrière", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON> de la page", "key.immersive_aircraft.fallback_control_down": "En bas", "key.immersive_aircraft.fallback_control_pull": "Contrôleur de traction", "key.immersive_aircraft.fallback_control_push": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "key.immersive_aircraft.fallback_use": "Utiliser l'arme/la monture", "key.immersive_aircraft.dismount": "Démontage", "key.immersive_aircraft.boost": "Coup de pouce de la fusée", "entity.immersive_aircraft.airship": "Dirigeable", "entity.immersive_aircraft.cargo_airship": "Dirigeable cargo", "entity.immersive_aircraft.warship": "<PERSON><PERSON><PERSON> de guerre", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON>", "item.immersive_aircraft.engine": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.sail": "Voile", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.eco_engine": "Moteur écologique", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.steel_boiler": "Chaudière en acier", "item.immersive_aircraft.industrial_gears": "Engrenages industriels", "item.immersive_aircraft.sturdy_pipes": "Des tuyaux robustes", "item.immersive_aircraft.gyroscope": "Gyroscope", "item.immersive_aircraft.hull_reinforcement": "Renforcement de la coque", "item.immersive_aircraft.improved_landing_gear": "Train d'atterrissage amélioré", "item.immersive_aircraft.rotary_cannon": "Canon rotatif", "item.immersive_aircraft.bomb_bay": "Baie des bombes", "item.immersive_aircraft.telescope": "Télescope", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON>de", "item.immersive_aircraft.rotary_cannon.description": "Canon à tir rapide fonctionnant avec de la poudre à canon.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON>pose des TNT, ne détruit pas les blocs mais inflige de lourds dégâts.", "item.immersive_aircraft.telescope.description": "Une version plus volumineuse de la lorgnette.", "item.immersive_aircraft.heavy_crossbow.description": "Une arbalète lourde avec un puissant coup de poing, nécessite des flèches.", "item.immersive_aircraft.item.upgrade": "Modernisation des aéronefs", "item.immersive_aircraft.item.weapon": "Arme de l'avion", "item.immersive_aircraft.airship": "Dirigeable", "item.immersive_aircraft.cargo_airship": "Dirigeable cargo", "item.immersive_aircraft.warship": "<PERSON><PERSON><PERSON> de guerre", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Les dirigeables ne sont peut-être pas le véhicule le plus rapide, mais ils sont certainement faciles à manœuvrer.", "item.immersive_aircraft.cargo_airship.description": "Lent et gourmand en carburant, mais il transporte tout un stock.", "item.immersive_aircraft.warship.description": "Une forteresse volante, lente mais lourdement armée.", "item.immersive_aircraft.biplane.description": "Rapide et plutôt fiable. Assure-toi que ta piste d'atterrissage est suffisamment longue.", "item.immersive_aircraft.gyrodyne.description": "Qui a besoin d'un moteur si on peut propulser l'avion avec de la pure force ? Donne-lui une bonne poussée et il s'envole !", "item.immersive_aircraft.quadrocopter.description": "Un chef-d'œuvre d'ingénierie ! 4 rotors attachés à du bambou. Parfait pour construire, et c'est à peu près tout.", "immersive_aircraft.gyrodyne_target": "%d%% Puissance, continue à pousser !", "immersive_aircraft.gyrodyne_target_reached": "Vitesse minimale du rotor atteinte, prêt pour le décollage !", "immersive_aircraft.invalid_dimension": "Cet avion ne fonctionne pas dans cette dimension.", "immersive_aircraft.out_of_ammo": "Plus de munitions !", "immersive_aircraft.repair": "%s%% réparé !", "immersive_aircraft.tried_dismount": "Appuie à nouveau pour sauter !", "immersive_aircraft.fuel.none": "Pas de carburant !", "immersive_aircraft.fuel.out": "Vous êtes à court de carburant !", "immersive_aircraft.fuel.low": "Vous manquez de carburant !", "immersive_aircraft.fat.none": "Pas de nourriture !", "immersive_aircraft.fat.out": "Tu as trop faim pour voler !", "option.immersive_aircraft.general": "Options générales", "option.immersive_aircraft.separateCamera": "Utilise une caméra séparée dans les avions.", "option.immersive_aircraft.useThirdPersonByDefault": "La caméra par défaut est celle de la troisième personne dans l'avion.", "option.immersive_aircraft.enableTrails": "Des sentiers de vapeur fantaisistes.", "option.immersive_aircraft.enableAnimatedSails": "Des voiles ondulantes.", "option.immersive_aircraft.renderDistance": "Rendre la distance en blocs.", "option.immersive_aircraft.fuelConsumption": "Taux de combustion du carburant.", "option.immersive_aircraft.windClearWeather": "Effet du vent de base.", "option.immersive_aircraft.windRainWeather": "Vent à la tombée de la pluie.", "option.immersive_aircraft.windThunderWeather": "Vent supplémentaire au tonnerre.", "option.immersive_aircraft.repairSpeed": "Réparation par clic.", "option.immersive_aircraft.repairExhaustion": "Épuisement du joueur par clic.", "option.immersive_aircraft.collisionDamage": "Dommages par collision.", "option.immersive_aircraft.burnFuelInCreative": "Brûlez du carburant en mode créatif.", "option.immersive_aircraft.acceptVanillaFuel": "Accepter le carburant vanille.", "option.immersive_aircraft.useCustomKeybindSystem": "Utilise des liaisons multi-key, peut casser certains mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Rendre la jauge du moteur au-dessus de la barre de chauffe.", "option.immersive_aircraft.enableCrashExplosion": "Activer l'explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Permet une explosion destructrice.", "option.immersive_aircraft.enableCrashFire": "Permettre une explosion ardente.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON> de l'explosion de l'accident.", "option.immersive_aircraft.crashDamage": "Endommage le joueur lors d'un crash.", "option.immersive_aircraft.preventKillThroughCrash": "Ne tue pas le joueur en cas de crash.", "option.immersive_aircraft.healthBarRow": "<PERSON><PERSON><PERSON> la barre de santé des véhicules.", "option.immersive_aircraft.damagePerHealthPoint": "Des valeurs plus élevées rendent les avions plus durables.", "option.immersive_aircraft.weaponsAreDestructive": "Autorise certaines armes à détruire des blocs.", "option.immersive_aircraft.dropInventory": "Dépose l'inventaire, au lieu de l'enregistrer dans le poste de l'avion.", "option.immersive_aircraft.dropUpgrades": "Laisse tomber les améliorations et la personnalisation, au lieu de les sauvegarder dans l'article de l'avion.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Régénère automatiquement la santé à chaque tick donné, simulant le comportement vanilla.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON><PERSON> uniquement en maintenant la touche Shift enfoncée, sinon entrez simplement dans le véhicule.", "immersive_aircraft.tooltip.no_target": "Doit être assemblé sur le sol !", "immersive_aircraft.tooltip.no_space": "Il n'y a pas assez d'espace !", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON> d'appoint", "immersive_aircraft.slot.weapon": "Emplacement d'arme", "immersive_aircraft.slot.boiler": "Fente pour le carburant", "immersive_aircraft.slot.banner": "Emplacement de la bannière", "immersive_aircraft.slot.dye": "Fente de teinture", "immersive_aircraft.slot.upgrade": "<PERSON>te de mise à niveau", "immersive_aircraft.upgrade.enginespeed": "%s%% de la puissance du moteur", "immersive_aircraft.upgrade.friction": "%s%% frottement de l'air", "immersive_aircraft.upgrade.acceleration": "%s%% de la vitesse de décollage", "immersive_aircraft.upgrade.durability": "%s%% durabilité", "immersive_aircraft.upgrade.fuel": "%s%% des besoins en carburant", "immersive_aircraft.upgrade.wind": "%s%% effet du vent", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliser", "immersive_aircraft.tooltip.inventory": "Contient %s articles."}