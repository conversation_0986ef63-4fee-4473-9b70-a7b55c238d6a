{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "沉浸式飛機", "key.immersive_aircraft.multi_control_left": "向左", "key.immersive_aircraft.multi_control_right": "向右", "key.immersive_aircraft.multi_control_forward": "前進", "key.immersive_aircraft.multi_control_backward": "後退", "key.immersive_aircraft.multi_control_up": "上升", "key.immersive_aircraft.multi_control_down": "下降", "key.immersive_aircraft.multi_control_pull": "向後拉杆", "key.immersive_aircraft.multi_control_push": "向前推杆", "key.immersive_aircraft.multi_use": "使用武器/載具", "key.immersive_aircraft.fallback_control_left": "向左", "key.immersive_aircraft.fallback_control_right": "向右", "key.immersive_aircraft.fallback_control_forward": "前進", "key.immersive_aircraft.fallback_control_backward": "後退", "key.immersive_aircraft.fallback_control_up": "上升", "key.immersive_aircraft.fallback_control_down": "下降", "key.immersive_aircraft.fallback_control_pull": "向後拉杆", "key.immersive_aircraft.fallback_control_push": "向前推杆", "key.immersive_aircraft.fallback_use": "使用武器/載具", "key.immersive_aircraft.dismount": "離開载具", "key.immersive_aircraft.boost": "火箭助推", "entity.immersive_aircraft.airship": "飛艇", "entity.immersive_aircraft.cargo_airship": "貨運飛艇", "entity.immersive_aircraft.warship": "戰鬥飛艇", "entity.immersive_aircraft.biplane": "雙翼飛機", "entity.immersive_aircraft.gyrodyne": "直升機", "entity.immersive_aircraft.quadrocopter": "四軸飛行器", "item.immersive_aircraft.hull": "飛機外殼", "item.immersive_aircraft.engine": "引擎", "item.immersive_aircraft.sail": "帆", "item.immersive_aircraft.propeller": "螺旋槳", "item.immersive_aircraft.boiler": "鍋爐", "item.immersive_aircraft.enhanced_propeller": "加固螺旋槳", "item.immersive_aircraft.eco_engine": "新能源引擎", "item.immersive_aircraft.nether_engine": "地獄引擎", "item.immersive_aircraft.steel_boiler": "鋼製鍋爐", "item.immersive_aircraft.industrial_gears": "工業齒輪", "item.immersive_aircraft.sturdy_pipes": "加固管道", "item.immersive_aircraft.gyroscope": "陀螺儀", "item.immersive_aircraft.hull_reinforcement": "加固套件", "item.immersive_aircraft.improved_landing_gear": "改良起落架", "item.immersive_aircraft.rotary_cannon": "輕型機槍", "item.immersive_aircraft.bomb_bay": "投彈倉", "item.immersive_aircraft.telescope": "機載望遠鏡", "item.immersive_aircraft.heavy_crossbow": "機載弩箭", "item.immersive_aircraft.rotary_cannon.description": "使用火藥的輕型機槍，攻擊速度快但傷害不高", "item.immersive_aircraft.bomb_bay.description": "投下TNT來對地面造成殺傷，不會摧毀方塊", "item.immersive_aircraft.telescope.description": "望遠鏡的笨重版本", "item.immersive_aircraft.heavy_crossbow.description": "裝載在飛機上的弩箭型武器，簡單可靠又容易使用", "item.immersive_aircraft.item.upgrade": "飛機升級", "item.immersive_aircraft.item.weapon": "飛機裝備", "item.immersive_aircraft.airship": "飛艇", "item.immersive_aircraft.cargo_airship": "貨運飛艇", "item.immersive_aircraft.warship": "戰鬥飛艇", "item.immersive_aircraft.biplane": "雙翼飛機", "item.immersive_aircraft.gyrodyne": "直升機", "item.immersive_aircraft.quadrocopter": "四軸飛行器", "item.immersive_aircraft.airship.description": "飛艇可飛的的不快，但駕駛起来很輕鬆。", "item.immersive_aircraft.cargo_airship.description": "緩慢又耗油，但可以載很多貨物.", "item.immersive_aircraft.warship.description": "一座飛行要塞，速度緩慢但全副武裝。", "item.immersive_aircraft.biplane.description": "雙翼飛機飛行速度快，安全性也很有保障。不過記得，跑道要夠長哦！", "item.immersive_aircraft.gyrodyne.description": "直升機，但是纯人力發動。使勁一推，你就能上天！", "item.immersive_aircraft.quadrocopter.description": "坐在四軸飛行器上建造屬於你的建築奇蹟！", "immersive_aircraft.gyrodyne_target": "推力達到 %d%%，再加把勁！", "immersive_aircraft.gyrodyne_target_reached": "到達達螺旋槳最低速度要求，準備起飛！", "immersive_aircraft.invalid_dimension": "這架飛機不能在這個維度飛行!", "immersive_aircraft.out_of_ammo": "沒有彈藥了！", "immersive_aircraft.repair": "%s%% 已修復!", "immersive_aircraft.tried_dismount": "再次按下以離開飛機", "immersive_aircraft.fuel.none": "沒有燃料！", "immersive_aircraft.fuel.out": "你的燃料用完了！", "immersive_aircraft.fuel.low": "你的燃料不足！", "immersive_aircraft.fat.none": "沒有食物！", "immersive_aircraft.fat.out": "你太餓了，不能再飛了，吃個東西休息一下！", "option.immersive_aircraft.general": "通用設定", "option.immersive_aircraft.separateCamera": "在飛機上設置單獨的視角", "option.immersive_aircraft.useThirdPersonByDefault": "在飛機上預設為第三人稱視角", "option.immersive_aircraft.enableTrails": "漂亮的飛機尾流", "option.immersive_aircraft.enableAnimatedSails": "風帆動畫", "option.immersive_aircraft.renderDistance": "渲染距離（單位: 方塊）", "option.immersive_aircraft.fuelConsumption": "燃料燃速率率。", "option.immersive_aircraft.windClearWeather": "基礎風阻。", "option.immersive_aircraft.windRainWeather": "降雨時的風。", "option.immersive_aircraft.windThunderWeather": "打雷時有更大的風。", "option.immersive_aircraft.repairSpeed": "每下點擊的修復量", "option.immersive_aircraft.repairExhaustion": "玩家每次點擊的疲勞度。", "option.immersive_aircraft.collisionDamage": "碰撞損傷。 ", "option.immersive_aircraft.burnFuelInCreative": "在創造模式中需要燃燒燃料。", "option.immersive_aircraft.acceptVanillaFuel": "接受原版燃料。", "option.immersive_aircraft.useCustomKeybindSystem": "使用多鍵绑定，可能會破壞某些模組。", "option.immersive_aircraft.showHotbarEngineGauge": "在快捷欄上顯示引擎儀表", "option.immersive_aircraft.enableCrashExplosion": "啟用爆炸.", "option.immersive_aircraft.enableCrashBlockDestruction": "啟用破壞性爆炸", "option.immersive_aircraft.enableCrashFire": "啟用火焰爆炸", "option.immersive_aircraft.crashExplosionRadius": "墜毀時爆炸範圍", "option.immersive_aircraft.crashDamage": "撞擊時對玩家造成傷害.", "option.immersive_aircraft.preventKillThroughCrash": "墜毀時不會殺死玩家.", "option.immersive_aircraft.healthBarRow": "抵消載具的生命值。", "option.immersive_aircraft.damagePerHealthPoint": "數值越高，飛機越耐用。", "option.immersive_aircraft.weaponsAreDestructive": "允許特定武器破壞方塊。", "option.immersive_aircraft.dropInventory": "載具損毀時，掉落載具內儲存的物品，而不是保存於載具中。", "option.immersive_aircraft.dropUpgrades": "載具損毀時，掉落載具裝備，而不是保存在載具中。", "option.immersive_aircraft.regenerateHealthEveryNTicks": "每隔一格自動再生健康值，模擬原版行為。", "option.immersive_aircraft.requireShiftForRepair": "只有在保持換檔時才能進行維修，否則只需進入車內即可。", "immersive_aircraft.tooltip.no_target": "你不能在天上組裝！", "immersive_aircraft.tooltip.no_space": "空間不足！", "immersive_aircraft.slot.booster": "助推火箭", "immersive_aircraft.slot.weapon": "裝備槽", "immersive_aircraft.slot.boiler": "燃料欄", "immersive_aircraft.slot.banner": "旗幟欄", "immersive_aircraft.slot.dye": "染料欄", "immersive_aircraft.slot.upgrade": "升級欄", "immersive_aircraft.upgrade.enginespeed": "%s%% 發動機功率", "immersive_aircraft.upgrade.friction": "%s%% 空氣阻力", "immersive_aircraft.upgrade.acceleration": "%s%% 起飛速度", "immersive_aircraft.upgrade.durability": "%s%% 耐久度", "immersive_aircraft.upgrade.fuel": "%s%% 燃料消耗", "immersive_aircraft.upgrade.wind": "%s%% 風力影響", "immersive_aircraft.upgrade.stabilizer": "%s%% 穩定度", "immersive_aircraft.tooltip.inventory": "包含 %s 件物品。"}