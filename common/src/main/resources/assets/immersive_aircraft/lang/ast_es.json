{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aeronaves Immersives", "key.immersive_aircraft.multi_control_left": "Iz<PERSON>erda", "key.immersive_aircraft.multi_control_right": "Derecha", "key.immersive_aircraft.multi_control_forward": "Adelante", "key.immersive_aircraft.multi_control_backward": "Atrás", "key.immersive_aircraft.multi_control_up": "Arriba", "key.immersive_aircraft.multi_control_down": "Abajo", "key.immersive_aircraft.multi_control_pull": "Controlador de tirón", "key.immersive_aircraft.multi_control_push": "Controlador de empuje", "key.immersive_aircraft.multi_use": "Usar arma/montar", "key.immersive_aircraft.fallback_control_left": "Iz<PERSON>erda", "key.immersive_aircraft.fallback_control_right": "Derecha", "key.immersive_aircraft.fallback_control_forward": "Adelante", "key.immersive_aircraft.fallback_control_backward": "Atrás", "key.immersive_aircraft.fallback_control_up": "Arriba", "key.immersive_aircraft.fallback_control_down": "Abajo", "key.immersive_aircraft.fallback_control_pull": "Controlador de tirón", "key.immersive_aircraft.fallback_control_push": "Controlador de empuje", "key.immersive_aircraft.fallback_use": "Usar arma/montar", "key.immersive_aircraft.dismount": "Desmontar", "key.immersive_aircraft.boost": "Impulso de cohete", "entity.immersive_aircraft.airship": "Zepelín", "entity.immersive_aircraft.cargo_airship": "Aeronaute de carga", "entity.immersive_aircraft.warship": "Buque de guerra", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "<PERSON>dino", "entity.immersive_aircraft.quadrocopter": "Cuadrocóptero", "item.immersive_aircraft.hull": "Casco", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "Cale<PERSON>ctor", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.eco_engine": "Motor Ecológico", "item.immersive_aircraft.nether_engine": "Motor del Nether", "item.immersive_aircraft.steel_boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.industrial_gears": "Engranajes Industriales", "item.immersive_aircraft.sturdy_pipes": "Tuberías Resistentes", "item.immersive_aircraft.gyroscope": "Giroscopio", "item.immersive_aircraft.hull_reinforcement": "Reforzamiento del Casco", "item.immersive_aircraft.improved_landing_gear": "Tren de Aterrizaje <PERSON>", "item.immersive_aircraft.rotary_cannon": "Cañón rotativo", "item.immersive_aircraft.bomb_bay": "Bahía de bombas", "item.immersive_aircraft.telescope": "Telescopio", "item.immersive_aircraft.heavy_crossbow": "Ballesta pesada", "item.immersive_aircraft.rotary_cannon.description": "Cañón de disparo rápido que funciona con pólvora.", "item.immersive_aircraft.bomb_bay.description": "Lanza TNT, no destruye bloques pero causa un gran daño.", "item.immersive_aircraft.telescope.description": "Una versión más gruesa del catalejo.", "item.immersive_aircraft.heavy_crossbow.description": "Una ballesta pesada con un potente golpe, requiere flechas.", "item.immersive_aircraft.item.upgrade": "Actualización de Aeronave", "item.immersive_aircraft.item.weapon": "Arma aeronáutica", "item.immersive_aircraft.airship": "Zepelín", "item.immersive_aircraft.cargo_airship": "Aeronaute de carga", "item.immersive_aircraft.warship": "Buque de guerra", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "<PERSON>dino", "item.immersive_aircraft.quadrocopter": "Cuadrocóptero", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Despaciu y consumidor de combustible, pero lleva un almacén enteru.", "item.immersive_aircraft.warship.description": "Una fortaleza voladora, lenta pero muy armada.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON><PERSON><PERSON> y bastante fiable. Asegúrate de que tu pista ye lo suficientemente llarga.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Potencia, sigue adelante!", "immersive_aircraft.gyrodyne_target_reached": "Velocidad mínima del rotor alcanzada, ¡listo para el despegue!", "immersive_aircraft.invalid_dimension": "Esta aeronave nun funciona en esta dimensión.", "immersive_aircraft.out_of_ammo": "¡Sin munición!", "immersive_aircraft.repair": "%s%% reparado!", "immersive_aircraft.tried_dismount": "¡Presiona de nuevo para saltar!", "immersive_aircraft.fuel.none": "¡Sin combustible!", "immersive_aircraft.fuel.out": "¡Te quedaste sin combustible!", "immersive_aircraft.fuel.low": "¡Estás bajo de combustible!", "immersive_aircraft.fat.none": "¡Sin comida!", "immersive_aircraft.fat.out": "¡Tienes demasiado hambre para volar!", "option.immersive_aircraft.general": "Opciones Generales", "option.immersive_aircraft.separateCamera": "Utiliza una cámara separada en la aeronave.", "option.immersive_aircraft.useThirdPersonByDefault": "Por defecto utiliza la cámara en tercera persona en la aeronave.", "option.immersive_aircraft.enableTrails": "Rastros de vapor elegantes.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON> on<PERSON>.", "option.immersive_aircraft.renderDistance": "Distancia de visualización en bloques.", "option.immersive_aircraft.fuelConsumption": "Tasa de consumo de combustible.", "option.immersive_aircraft.windClearWeather": "Efecto del viento base.", "option.immersive_aircraft.windRainWeather": "Viento en la lluvia.", "option.immersive_aircraft.windThunderWeather": "Viento extra durante el trueno.", "option.immersive_aircraft.repairSpeed": "Reparar por clic.", "option.immersive_aircraft.repairExhaustion": "Exhaustión del jugador por clic.", "option.immersive_aircraft.collisionDamage": "Daño por colisión.", "option.immersive_aircraft.burnFuelInCreative": "Quema combustible en modo creativo.", "option.immersive_aircraft.acceptVanillaFuel": "Aceptar combustible de vainilla.", "option.immersive_aircraft.useCustomKeybindSystem": "Usar múltiples teclas de acceso, puede romper ciertos mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderiza el medidor del motor sobre la barra de acceso rápido.", "option.immersive_aircraft.enableCrashExplosion": "Activar explosión.", "option.immersive_aircraft.enableCrashBlockDestruction": "Activar explosión destructiva.", "option.immersive_aircraft.enableCrashFire": "Activar explosión de fuego.", "option.immersive_aircraft.crashExplosionRadius": "Tamaño de la explosión al chocar.", "option.immersive_aircraft.crashDamage": "Daña al jugador en un choque.", "option.immersive_aircraft.preventKillThroughCrash": "No matará al jugador en un choque.", "option.immersive_aircraft.healthBarRow": "Desplazamiento de la barra de salud de los vehículos.", "option.immersive_aircraft.damagePerHealthPoint": "Valores más altos hacen que las aeronaves sean más duraderas.", "option.immersive_aircraft.weaponsAreDestructive": "Permitir a algunos armes destruir bloques.", "option.immersive_aircraft.dropInventory": "Deixa caer el inventariu, enlloc de salvarlu dentro del item d'aviación.", "option.immersive_aircraft.dropUpgrades": "Deixa caer les millores y personalización, enlloc de salvarles dentro del item d'aviación.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenera la salud automáticamente cada tic determinado, simulando el comportamientu vanilla.", "option.immersive_aircraft.requireShiftForRepair": "Solamente repara cuando mantienes shift, en otro caso entra na vehículo.", "immersive_aircraft.tooltip.no_target": "¡Necesita ser assembláu sobre el chanu!", "immersive_aircraft.tooltip.no_space": "¡No hai abbastanza espacio!", "immersive_aircraft.slot.booster": "Cohetes de impulso", "immersive_aircraft.slot.weapon": "<PERSON><PERSON><PERSON> de arma", "immersive_aircraft.slot.boiler": "Ranura de combustible", "immersive_aircraft.slot.banner": "Ra<PERSON><PERSON> de <PERSON>", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON> de tinte", "immersive_aircraft.slot.upgrade": "Ranura de actualización", "immersive_aircraft.upgrade.enginespeed": "%s%% potencia del motor", "immersive_aircraft.upgrade.friction": "%s%% fricción del aire", "immersive_aircraft.upgrade.acceleration": "%s%% velocidad de despegue", "immersive_aircraft.upgrade.durability": "%s%% durabilidad", "immersive_aircraft.upgrade.fuel": "%s%% requisitos de combustible", "immersive_aircraft.upgrade.wind": "%s%% efecto del viento", "immersive_aircraft.upgrade.stabilizer": "%s%% estabilizando", "immersive_aircraft.tooltip.inventory": "Contien %s artí<PERSON>los."}