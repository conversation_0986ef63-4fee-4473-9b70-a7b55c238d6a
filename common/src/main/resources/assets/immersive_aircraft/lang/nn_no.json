{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive fly", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Fremover", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_down": "<PERSON>", "key.immersive_aircraft.multi_control_pull": "Dra til kontrolleren", "key.immersive_aircraft.multi_control_push": "Skyv kontrolleren", "key.immersive_aircraft.multi_use": "Bruk våpen/montering", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Fremover", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_down": "<PERSON>", "key.immersive_aircraft.fallback_control_pull": "Dra til kontrolleren", "key.immersive_aircraft.fallback_control_push": "Skyv kontrolleren", "key.immersive_aircraft.fallback_use": "Bruk våpen/montering", "key.immersive_aircraft.dismount": "Gå av", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "Luftskip", "entity.immersive_aircraft.cargo_airship": "Cargo luftskip", "entity.immersive_aircraft.warship": "Krigsskip", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrokopter", "item.immersive_aircraft.hull": "Skrog", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Seil", "item.immersive_aircraft.propeller": "Propell", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON> propell", "item.immersive_aircraft.eco_engine": "Øko-motor", "item.immersive_aircraft.nether_engine": "Nether-motor", "item.immersive_aircraft.steel_boiler": "St<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.industrial_gears": "Industrielle gir", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON> rø<PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Skrogforsterkning", "item.immersive_aircraft.improved_landing_gear": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon": "Rotary-kanon", "item.immersive_aircraft.bomb_bay": "Bombebay", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "Raskskytande kanon som går med krut.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON><PERSON>, øydelegg ikkje blokker men gir stor skade.", "item.immersive_aircraft.telescope.description": "Ein bulkete versjon av kikkerten.", "item.immersive_aircraft.heavy_crossbow.description": "Ein tung armbrøst med stor kraft, krev piler.", "item.immersive_aircraft.item.upgrade": "Flyoppgradering", "item.immersive_aircraft.item.weapon": "Flyvåpen", "item.immersive_aircraft.airship": "Luftskip", "item.immersive_aircraft.cargo_airship": "Cargo luftskip", "item.immersive_aircraft.warship": "Krigsskip", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrokopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Saktekne og drivstoffhungrig men fraktar ei heil lagringsskap.", "item.immersive_aircraft.warship.description": "Ein flygande festning, langsom men tungt bevæpna.", "item.immersive_aircraft.biplane.description": "Rask og ganske pålitelig. <PERSON><PERSON><PERSON> for at rullebanen din er lang nok.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% kraft, fortsett å presse!", "immersive_aircraft.gyrodyne_target_reached": "<PERSON>ste <PERSON>et nådd, klar for avgang!", "immersive_aircraft.invalid_dimension": "<PERSON>te flyet fungerer ikkje i denne dimensjonen.", "immersive_aircraft.out_of_ammo": "Ute av ammunisjon!", "immersive_aircraft.repair": "%s%% reparert!", "immersive_aircraft.tried_dismount": "Trykk igjen for å hoppe ut!", "immersive_aircraft.fuel.none": "Ingen drivstoff!", "immersive_aircraft.fuel.out": "Du er ute av drivstoff!", "immersive_aircraft.fuel.low": "Du har lite drivstoff!", "immersive_aircraft.fat.none": "Ingen mat!", "immersive_aircraft.fat.out": "Du er for sulten til å fly!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "option.immersive_aircraft.separateCamera": "Bruk et eget kamera i flyet.", "option.immersive_aircraft.useThirdPersonByDefault": "Standard til tredje person kamera i fly.", "option.immersive_aircraft.enableTrails": "Fancy dampstriper.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON><PERSON> seil.", "option.immersive_aircraft.renderDistance": "Renderavstand i blokker.", "option.immersive_aircraft.fuelConsumption": "Drivstoffforbruksrate.", "option.immersive_aircraft.windClearWeather": "Grunnvind effekt.", "option.immersive_aircraft.windRainWeather": "Vind ved regn.", "option.immersive_aircraft.windThunderWeather": "Ekstra vind ved torden.", "option.immersive_aircraft.repairSpeed": "Reparasjon per klikk.", "option.immersive_aircraft.repairExhaustion": "Spelarsliten per klikk.", "option.immersive_aircraft.collisionDamage": "Kollisjonskade.", "option.immersive_aircraft.burnFuelInCreative": "Bruk drivstoff i kreativ modus.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "Bruk flere tastebindings, kan bryte enkelte modifikasjoner.", "option.immersive_aircraft.showHotbarEngineGauge": "<PERSON><PERSON> over <PERSON>ig<PERSON><PERSON><PERSON>.", "option.immersive_aircraft.enableCrashExplosion": "Aktiver eksplosjon.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktiver destruktiv eksplosjon.", "option.immersive_aircraft.enableCrashFire": "Aktiver branneksplosjon.", "option.immersive_aircraft.crashExplosionRadius": "Størrelse på krasj eksplosjon.", "option.immersive_aircraft.crashDamage": "Skade spelaren ved ein krasj.", "option.immersive_aircraft.preventKillThroughCrash": "Vil ikkje drepe spelaren ved ein krasj.", "option.immersive_aircraft.healthBarRow": "Offset helsebarane til køyretøya.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON><PERSON> verdiar gjer fly meir slitesterke.", "option.immersive_aircraft.weaponsAreDestructive": "Tillet enkelte våpen å øydelegge blokk.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON> inventaret, i staden for å lagre det innanfor flyet.", "option.immersive_aircraft.dropUpgrades": "Dropp oppgraderingane og tilpassingane, i staden for å lagre det innanfor flyet.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenererar automatisk helse for kvar føresett tick, og simulerer vanilja-atferd.", "option.immersive_aircraft.requireShiftForRepair": "Reparer berre når du held nede skift, elles berre gå inn i kjøretøyet.", "immersive_aircraft.tooltip.no_target": "<PERSON>å assemblerast på golvet!", "immersive_aircraft.tooltip.no_space": "Ikkje nok plass!", "immersive_aircraft.slot.booster": "Boostraketter", "immersive_aircraft.slot.weapon": "Våpenslot", "immersive_aircraft.slot.boiler": "Drivstoffluke", "immersive_aircraft.slot.banner": "Bannerluke", "immersive_aircraft.slot.dye": "Fargestoffluke", "immersive_aircraft.slot.upgrade": "Oppgraderingsluke", "immersive_aircraft.upgrade.enginespeed": "%s%% motor kraft", "immersive_aircraft.upgrade.friction": "%s%% luftmotstand", "immersive_aircraft.upgrade.acceleration": "%s%% oppstartshastighet", "immersive_aircraft.upgrade.durability": "%s%% holdbarhet", "immersive_aircraft.upgrade.fuel": "%s%% drivstoffbehov", "immersive_aircraft.upgrade.wind": "%s%% vind effekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliserande", "immersive_aircraft.tooltip.inventory": "Inneheld %s gjenstandar."}