{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_left": "Kesker", "key.immersive_aircraft.multi_control_right": "Deus", "key.immersive_aircraft.multi_control_forward": "Forth", "key.immersive_aircraft.multi_control_backward": "Aback", "key.immersive_aircraft.multi_control_up": "Upp", "key.immersive_aircraft.multi_control_down": "Dha", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Usa'r weapon/hlus", "key.immersive_aircraft.fallback_control_left": "Kesker", "key.immersive_aircraft.fallback_control_right": "Deus", "key.immersive_aircraft.fallback_control_forward": "Forth", "key.immersive_aircraft.fallback_control_backward": "Aback", "key.immersive_aircraft.fallback_control_up": "Upp", "key.immersive_aircraft.fallback_control_down": "Dha", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Usa'r weapon/hlus", "key.immersive_aircraft.dismount": "Dismount", "key.immersive_aircraft.boost": "Rocket boost", "entity.immersive_aircraft.airship": "Aerafl", "entity.immersive_aircraft.cargo_airship": "Cargo Aershipping", "entity.immersive_aircraft.warship": "<PERSON><PERSON><PERSON>", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Gorv", "item.immersive_aircraft.engine": "Fur", "item.immersive_aircraft.sail": "Sail", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Propeller an-enhanced", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Steel Boiler", "item.immersive_aircraft.industrial_gears": "Fynyres di-organek", "item.immersive_aircraft.sturdy_pipes": "Pipes pryv", "item.immersive_aircraft.gyroscope": "Gyroscope", "item.immersive_aircraft.hull_reinforcement": "Reinforcement hull", "item.immersive_aircraft.improved_landing_gear": "Improved Landing Gear", "item.immersive_aircraft.rotary_cannon": "Poul Canna", "item.immersive_aircraft.bomb_bay": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.telescope": "Telescop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "Canna y'n fast-firing gans gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Drog TNT, na lynn y blocуs mes gans lleski treus.", "item.immersive_aircraft.telescope.description": "Un fordhans vras a’th spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>r crosbowe dregh gans peus pow, kledh john.", "item.immersive_aircraft.item.upgrade": "Upgrade aerafl", "item.immersive_aircraft.item.weapon": "Avio Weapons", "item.immersive_aircraft.airship": "Aerafl", "item.immersive_aircraft.cargo_airship": "Cargo Aershipping", "item.immersive_aircraft.warship": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Avo'n aredlish rag ta an gallos.", "item.immersive_aircraft.warship.description": "Fortress hevel gwayll, a slow gans arm sur.", "item.immersive_aircraft.biplane.description": "Hir and gwrys yn ragythen. Gwasg an runway a yl porth.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Powe, keep pushing!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotor speed a-goth, yn-berhedh!", "immersive_aircraft.invalid_dimension": "An avio ma na vynna gans an reyls ma.", "immersive_aircraft.out_of_ammo": "Dre an ammo!", "immersive_aircraft.repair": "%s%% gwasgel!", "immersive_aircraft.tried_dismount": "Gwasg yn dro da jy!", "immersive_aircraft.fuel.none": "Na fuell!", "immersive_aircraft.fuel.out": "Degyn a-rag fuell!", "immersive_aircraft.fuel.low": "Yes owth yn low on fuell!", "immersive_aircraft.fat.none": "Na food!", "immersive_aircraft.fat.out": "Yes owthyn sovr gury!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON>", "option.immersive_aircraft.separateCamera": "Usya camera separate yn aerafl.", "option.immersive_aircraft.useThirdPersonByDefault": "Defaults to third person camera yn a<PERSON><PERSON>l.", "option.immersive_aircraft.enableTrails": "Fanteg steam trails.", "option.immersive_aircraft.enableAnimatedSails": "Sails plyff.", "option.immersive_aircraft.renderDistance": "Render distans in blocks.", "option.immersive_aircraft.fuelConsumption": "Fowd else.", "option.immersive_aircraft.windClearWeather": "Effect wind bas.", "option.immersive_aircraft.windRainWeather": "Wind yn rain.", "option.immersive_aircraft.windThunderWeather": "Extra wind yn shad.", "option.immersive_aircraft.repairSpeed": "<PERSON>wasg an kella.", "option.immersive_aircraft.repairExhaustion": "<PERSON><PERSON><PERSON> en expires ow kelwel.", "option.immersive_aircraft.collisionDamage": "Collision damage.", "option.immersive_aircraft.burnFuelInCreative": "Burn fuell yn mode creative.", "option.immersive_aircraft.acceptVanillaFuel": "Accept fuell vanilla.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON>ya multi-keybindings, may break certain mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Render an engine gauge war an hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Pyns an explode.", "option.immersive_aircraft.enableCrashBlockDestruction": "Pyns an explode dieffrans.", "option.immersive_aircraft.enableCrashFire": "Pyns an explode furi.", "option.immersive_aircraft.crashExplosionRadius": "Fair an explode crash.", "option.immersive_aircraft.crashDamage": "Droug an player war an crash.", "option.immersive_aircraft.preventKillThroughCrash": "<PERSON> vynner an player diwar an crash.", "option.immersive_aircraft.healthBarRow": "Oset an bar hwelg a’r vehickles.", "option.immersive_aircraft.damagePerHealthPoint": "Vlaid vrasa gans an avio.", "option.immersive_aircraft.weaponsAreDestructive": "Agh esedh unnegh skry destrya bor.", "option.immersive_aircraft.dropInventory": "Dros an inventory, yns gans na dhe gewsel gans an aevitem.", "option.immersive_aircraft.dropUpgrades": "Dros an upgrades ha customization, yns gans na dhe gewsel gans an aevitem.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "<PERSON><PERSON> answers an health yn termyn cushys, dadleus ow slofors.", "option.immersive_aircraft.requireShiftForRepair": "Gans holden shift, gans na fizhyn an veihcly.", "immersive_aircraft.tooltip.no_target": "<PERSON><PERSON>s ow kelwel an floyer!", "immersive_aircraft.tooltip.no_space": "Nyns yn hager avel!", "immersive_aircraft.slot.booster": "Boost rockets", "immersive_aircraft.slot.weapon": "Slot Weapon", "immersive_aircraft.slot.boiler": "Slot fuels", "immersive_aircraft.slot.banner": "Slot banner", "immersive_aircraft.slot.dye": "Slot dye", "immersive_aircraft.slot.upgrade": "Slot upgrade", "immersive_aircraft.upgrade.enginespeed": "%s%% powe engine", "immersive_aircraft.upgrade.friction": "%s%% friction air", "immersive_aircraft.upgrade.acceleration": "%s%% speed a-goth", "immersive_aircraft.upgrade.durability": "%s%% durabilita", "immersive_aircraft.upgrade.fuel": "%s%% requirement fowl", "immersive_aircraft.upgrade.wind": "%s%% effect wind", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilis,", "immersive_aircraft.tooltip.inventory": "Gans %s e items."}