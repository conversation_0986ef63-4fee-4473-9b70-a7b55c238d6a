{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersiivne õ<PERSON>õiduk", "key.immersive_aircraft.multi_control_left": "Vasakpoolne", "key.immersive_aircraft.multi_control_right": "Õigus", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Tagasi", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_down": "<PERSON>a", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Kasutage relva/mount", "key.immersive_aircraft.fallback_control_left": "Vasakpoolne", "key.immersive_aircraft.fallback_control_right": "Õigus", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Tagasi", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_down": "<PERSON>a", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Kasutage relva/mount", "key.immersive_aircraft.dismount": "Ma<PERSON><PERSON><PERSON>", "key.immersive_aircraft.boost": "Raketi võimendus", "entity.immersive_aircraft.airship": "Õhulaev", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.warship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.biplane": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON>", "item.immersive_aircraft.engine": "Mootor", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "<PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Täiustatud propeller", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.industrial_gears": "Tööstuslikud käigud", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "item.immersive_aircraft.gyroscope": "Güroskoop", "item.immersive_aircraft.hull_reinforcement": "<PERSON><PERSON>", "item.immersive_aircraft.improved_landing_gear": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "Teleskoop", "item.immersive_aircraft.heavy_crossbow": "Raske vibulaskur", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON> tuli<PERSON> ka<PERSON>, mis töötab püssi<PERSON> abil.", "item.immersive_aircraft.bomb_bay.description": "Paiskab TNT-d, ei hävita plo<PERSON>, kuid tekitab suurt kah<PERSON>.", "item.immersive_aircraft.telescope.description": "Luureklaasi mahukam versioon.", "item.immersive_aircraft.heavy_crossbow.description": "Raske ja võimsa löögiga vibulaskur, vaja<PERSON> nooli.", "item.immersive_aircraft.item.upgrade": "Õhusõiduki uuendamine", "item.immersive_aircraft.item.weapon": "Õhusõiduki relv", "item.immersive_aircraft.airship": "Õhulaev", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.warship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.biplane": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Õhulaevad ei pruugi olla kõige kiiremad sõidukid, kuid neid on kindlasti lihtne manööverdada.", "item.immersive_aircraft.cargo_airship.description": "Aeglane ja <PERSON><PERSON><PERSON>, kuid kannab kogu varu.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON><PERSON> kindlus, a<PERSON><PERSON>, kuid raskelt relvastatud.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON> ja üsna usaldusväärne. Veenduge, et teie lennurada on piisavalt pikk.", "item.immersive_aircraft.gyrodyne.description": "<PERSON><PERSON><PERSON> on vaja mootorit, kui lennukit saaks käitada puhtalt jõuga? Andke sellele korralik tõuge ja see lendab!", "item.immersive_aircraft.quadrocopter.description": "Tehnika meistriteos! 4 rootorit, mis on kinnitatud bambuse külge. <PERSON><PERSON>ius<PERSON> ehita<PERSON>, ja see ongi kõik.", "immersive_aircraft.gyrodyne_target": "%d%% Power, jätkake pingutamist!", "immersive_aircraft.gyrodyne_target_reached": "Minimaalne rootori kiirus on saavutatud, valmis startimiseks!", "immersive_aircraft.invalid_dimension": "See lennuk ei tööta selles mõõtmes.", "immersive_aircraft.out_of_ammo": "Laskemoona on otsas!", "immersive_aircraft.repair": "%s%% parandatud!", "immersive_aircraft.tried_dismount": "Vaj<PERSON><PERSON> uuesti, et välja hü<PERSON>!", "immersive_aircraft.fuel.none": "<PERSON><PERSON><PERSON>t ei ole!", "immersive_aircraft.fuel.out": "Sul sai kütus otsa!", "immersive_aircraft.fuel.low": "Sul on kütus vähe!", "immersive_aircraft.fat.none": "Ei mingit toitu!", "immersive_aircraft.fat.out": "Sa oled liiga näljane, et lennata!", "option.immersive_aircraft.general": "Üldised valikud", "option.immersive_aircraft.separateCamera": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eraldi ka<PERSON>.", "option.immersive_aircraft.useThirdPersonByDefault": "<PERSON><PERSON>t on õhusõidukis kolmanda isiku kaamera.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON>.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON>lised purjed.", "option.immersive_aircraft.renderDistance": "Renderdamise kaugus plokkidena.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON><PERSON><PERSON> põlemiskii<PERSON>.", "option.immersive_aircraft.windClearWeather": "Baasituule mõju.", "option.immersive_aircraft.windRainWeather": "Tuul sademete korral.", "option.immersive_aircraft.windThunderWeather": "Täiendav tuul äikesel.", "option.immersive_aircraft.repairSpeed": "Remont ühe klõpsu kohta.", "option.immersive_aircraft.repairExhaustion": "Mängija kurnatus ühe klõ<PERSON>u kohta.", "option.immersive_aircraft.collisionDamage": "Kokkupõrkekahjustus.", "option.immersive_aircraft.burnFuelInCreative": "Põleta kütust loomingulises režii<PERSON>.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON><PERSON><PERSON> mitme v<PERSON>ü<PERSON>, võib rikkuda teatud modid.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderda mootorinäidik üle kuumariba.", "option.immersive_aircraft.enableCrashExplosion": "<PERSON><PERSON> plahvatus.", "option.immersive_aircraft.enableCrashBlockDestruction": "Võ<PERSON>ldab hävitava plahvatuse.", "option.immersive_aircraft.enableCrashFire": "<PERSON><PERSON><PERSON><PERSON><PERSON> tulise p<PERSON>.", "option.immersive_aircraft.crashExplosionRadius": "Kokkupõrke plahvatuse suurus.", "option.immersive_aircraft.crashDamage": "Kahjustada mängija kokkupõrke korral.", "option.immersive_aircraft.preventKillThroughCrash": "Ei tapa mängijat kokkupõrke korral.", "option.immersive_aircraft.healthBarRow": "<PERSON><PERSON><PERSON><PERSON>b s<PERSON>iduki<PERSON> terviseriba.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON>used muudavad õhusõiduki vastupidavamaks.", "option.immersive_aircraft.weaponsAreDestructive": "Luba mõnedel relvadel plokke hävitada.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON><PERSON> inventar, selle asem<PERSON>, et salvestada see õhusõid<PERSON> eseme sees.", "option.immersive_aircraft.dropUpgrades": "<PERSON><PERSON><PERSON> uuendused ja kohanda<PERSON>, selle as<PERSON><PERSON>, et salvestada see õhusõid<PERSON> eseme sees.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Taastab automaatselt tervist iga t<PERSON> j<PERSON>, simuleerides vanilla käitumist.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON> si<PERSON>, kui ho<PERSON>, vastasel juhul sisenege lihtsalt sõidukisse.", "immersive_aircraft.tooltip.no_target": "Peab olema kokku pandud p<PERSON>al!", "immersive_aircraft.tooltip.no_space": "<PERSON>uumi ei ole pii<PERSON>!", "immersive_aircraft.slot.booster": "Boost-raketid", "immersive_aircraft.slot.weapon": "Relvapesa", "immersive_aircraft.slot.boiler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.banner": "Bänneri pesa", "immersive_aircraft.slot.dye": "Värvipesa", "immersive_aircraft.slot.upgrade": "Upgrade pesa", "immersive_aircraft.upgrade.enginespeed": "%s%% mootori võimsus", "immersive_aircraft.upgrade.friction": "%s%% õhuhõõrdumine", "immersive_aircraft.upgrade.acceleration": "%s%% stardikiirus", "immersive_aircraft.upgrade.durability": "%s%% vastupidavus", "immersive_aircraft.upgrade.fuel": "%s%% kütusevajadus", "immersive_aircraft.upgrade.wind": "%s%% tuule mõju", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliseeriv", "immersive_aircraft.tooltip.inventory": "Sisaldab %s ."}