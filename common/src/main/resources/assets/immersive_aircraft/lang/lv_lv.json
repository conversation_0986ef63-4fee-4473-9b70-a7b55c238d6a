{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gaisa kuģi", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Uz priekšu", "key.immersive_aircraft.multi_control_backward": "Atpakaļ", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON> augšu", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "<PERSON><PERSON><PERSON><PERSON> ieroci/uzstā<PERSON><PERSON>t", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Uz priekšu", "key.immersive_aircraft.fallback_control_backward": "Atpakaļ", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON> augšu", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "<PERSON><PERSON><PERSON><PERSON> ieroci/uzstā<PERSON><PERSON>t", "key.immersive_aircraft.dismount": "Demontāž<PERSON>", "key.immersive_aircraft.boost": "Raķešu pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON>", "entity.immersive_aircraft.warship": "<PERSON> kuģis", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Kvadrokopters", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.sail": "Buru", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "<PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Uzlabots propellers", "item.immersive_aircraft.eco_engine": "<PERSON><PERSON>", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "<PERSON><PERSON><PERSON><PERSON> katls", "item.immersive_aircraft.industrial_gears": "Rūpnieciskie zobrati", "item.immersive_aircraft.sturdy_pipes": "<PERSON>z<PERSON><PERSON><PERSON> caurules", "item.immersive_aircraft.gyroscope": "Giroskops", "item.immersive_aircraft.hull_reinforcement": "Korpusa pastiprinājums", "item.immersive_aircraft.improved_landing_gear": "Uzlabota šasija", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "Teleskops", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON><PERSON> arbalets", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON> da<PERSON><PERSON><PERSON> a<PERSON>.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON><PERSON> TNT, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>, bet nodara lielus boj<PERSON><PERSON>.", "item.immersive_aircraft.telescope.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>jo<PERSON>īg<PERSON> vers<PERSON>.", "item.immersive_aircraft.heavy_crossbow.description": "Smags arbalets ar s<PERSON><PERSON> t<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> bult<PERSON>.", "item.immersive_aircraft.item.upgrade": "Gaisa kuģu modernizācija", "item.immersive_aircraft.item.weapon": "Gais<PERSON> kuģa ierocis", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.warship": "<PERSON> kuģis", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Kvadrokopters", "item.immersive_aircraft.airship.description": "Varb<PERSON>t dirižabļi nav visātrākie transportlīdzekļi, taču ar tiem ir viegli manevr<PERSON>t.", "item.immersive_aircraft.cargo_airship.description": "<PERSON><PERSON><PERSON> un deg<PERSON> i<PERSON>, bet pārvadā visu krātuvi.", "item.immersive_aircraft.warship.description": "Lidojo<PERSON><PERSON>, <PERSON><PERSON><PERSON>, bet <PERSON>i bru<PERSON>.", "item.immersive_aircraft.biplane.description": "Ātra un diezgan uzticama. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka skrejceļ<PERSON> ir pietiekami garš.", "item.immersive_aircraft.gyrodyne.description": "<PERSON><PERSON> v<PERSON><PERSON> d<PERSON>, ja lid<PERSON> var darbināt ar tīru spēku? Dodiet tam labu grūdienu, un tas lidos!", "item.immersive_aircraft.quadrocopter.description": "Inženierzinātņu meistardarbs! 4 rotor<PERSON>, kas piest<PERSON> pie bambusa. Ideāli piemē<PERSON> būv<PERSON>, un tas ir viss.", "immersive_aircraft.gyrodyne_target": "%d%% <PERSON><PERSON><PERSON>, turpini spiest!", "immersive_aircraft.gyrodyne_target_reached": "Sasniegts minimālais rotora ātrums, gatavs <PERSON>!", "immersive_aircraft.invalid_dimension": "<PERSON>is gaisa kuģis šajā dimensijā nedarbo<PERSON>.", "immersive_aircraft.out_of_ammo": "Munī<PERSON>ja beigusies!", "immersive_aircraft.repair": "%s%% salabots!", "immersive_aircraft.tried_dismount": "Nospiediet vēlreiz, lai izlēktu ārā!", "immersive_aircraft.fuel.none": "Degvielas nav!", "immersive_aircraft.fuel.out": "<PERSON><PERSON> be<PERSON> de<PERSON>!", "immersive_aircraft.fuel.low": "Jums ir maz deg<PERSON>!", "immersive_aircraft.fat.none": "Bez ēdiena!", "immersive_aircraft.fat.out": "Jūs esat pārāk i<PERSON>, lai lidotu!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "option.immersive_aircraft.separateCamera": "Lidmašīnā izmantojiet atsevišķu kameru.", "option.immersive_aircraft.useThirdPersonByDefault": "<PERSON><PERSON><PERSON> noklusējuma tiek iestatīta trešās personas kamera <PERSON>.", "option.immersive_aircraft.enableTrails": "Fancy tvaika takas.", "option.immersive_aircraft.enableAnimatedSails": "Wavey wavey buras.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> blo<PERSON>.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.windClearWeather": "Bāzes vēja ietekme.", "option.immersive_aircraft.windRainWeather": "Vējš pie nokrišņiem.", "option.immersive_aircraft.windThunderWeather": "Papildu vējš pie pērkona.", "option.immersive_aircraft.repairSpeed": "Remonts pēc klikšķa.", "option.immersive_aircraft.repairExhaustion": "Spēlēt<PERSON><PERSON> izsmelšana par klikšķi.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON><PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON>zman<PERSON>t multi-keybindings, var saboj<PERSON>t dažus modus.", "option.immersive_aircraft.showHotbarEngineGauge": "Pārveidojiet motora rādītā<PERSON> virs karstā<PERSON> j<PERSON>.", "option.immersive_aircraft.enableCrashExplosion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.enableCrashBlockDestruction": "<PERSON>es<PERSON><PERSON><PERSON><PERSON> s<PERSON>r<PERSON>.", "option.immersive_aircraft.enableCrashFire": "Iespējams ugunīgs sprādziens.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprā<PERSON><PERSON>na <PERSON>.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON><PERSON><PERSON> spēl<PERSON>t<PERSON><PERSON> avārijas gadīju<PERSON>.", "option.immersive_aircraft.preventKillThroughCrash": "Neiznīcina spēlētāju avārijas gadījumā.", "option.immersive_aircraft.healthBarRow": "Kompensē transportlīdzekļu veselības j<PERSON>lu.", "option.immersive_aircraft.damagePerHealthPoint": "Lie<PERSON>ā<PERSON> vērtības padara gaisa kuģi izturīgāku.", "option.immersive_aircraft.weaponsAreDestructive": "Ļaujiet dažiem ieročiem iz<PERSON>īcināt blo<PERSON>.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>, nevis sagla<PERSON>et to lidaparāta vienī<PERSON>.", "option.immersive_aircraft.dropUpgrades": "Izmetiet uzlabojumus un pielāgojumus, nevis saglabājiet tos lidaparāta vienībā.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automātiski atjauno veselību ik reizi, imit<PERSON><PERSON><PERSON> vaniļ<PERSON> uzvedību.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON> veiciet tikai tad, ja turat pā<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>, pretējā gad<PERSON> vienkārši iebrauciet transportlīdzeklī.", "immersive_aircraft.tooltip.no_target": "Nepieciešams sa<PERSON>ēt uz grīdas!", "immersive_aircraft.tooltip.no_space": "Nepietiek vietas!", "immersive_aircraft.slot.booster": "Palielin<PERSON><PERSON> raķetes", "immersive_aircraft.slot.weapon": "Ieroču slots", "immersive_aircraft.slot.boiler": "<PERSON><PERSON><PERSON><PERSON> slots", "immersive_aircraft.slot.banner": "Banner slots", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.upgrade.enginespeed": "%s%% dzinēja jauda", "immersive_aircraft.upgrade.friction": "%s%% gaisa berze", "immersive_aircraft.upgrade.acceleration": "%s%% pacelšanās ātrums", "immersive_aircraft.upgrade.durability": "%s%% ilgmūžība", "immersive_aircraft.upgrade.fuel": "%s%% deg<PERSON>las nepieciešamība", "immersive_aircraft.upgrade.wind": "%s%% vēja ietekme", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizē", "immersive_aircraft.tooltip.inventory": "Satur %s vienumus."}