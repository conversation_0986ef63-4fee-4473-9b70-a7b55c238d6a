{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Op", "key.immersive_aircraft.multi_control_down": "<PERSON>", "key.immersive_aircraft.multi_control_pull": "Træk styreenhed", "key.immersive_aircraft.multi_control_push": "Tryk styreenhed", "key.immersive_aircraft.multi_use": "Brug våben/montering", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Op", "key.immersive_aircraft.fallback_control_down": "<PERSON>", "key.immersive_aircraft.fallback_control_pull": "Træk styreenhed", "key.immersive_aircraft.fallback_control_push": "Tryk styreenhed", "key.immersive_aircraft.fallback_use": "Brug våben/montering", "key.immersive_aircraft.dismount": "Stige af", "key.immersive_aircraft.boost": "Raket boost", "entity.immersive_aircraft.airship": "Luftskib", "entity.immersive_aircraft.cargo_airship": "Luftskib til fragt", "entity.immersive_aircraft.warship": "Krigsskib", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "Trædekopter", "entity.immersive_aircraft.quadrocopter": "Quadrokopter", "item.immersive_aircraft.hull": "Skrog", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propel", "item.immersive_aircraft.boiler": "<PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON> propel", "item.immersive_aircraft.eco_engine": "Økomotor", "item.immersive_aircraft.nether_engine": "Nethermotor", "item.immersive_aircraft.steel_boiler": "St<PERSON><PERSON>edel", "item.immersive_aircraft.industrial_gears": "Industriel<PERSON>", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON> rø<PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Skrogforstærkning", "item.immersive_aircraft.improved_landing_gear": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON> kanon", "item.immersive_aircraft.bomb_bay": "Bombebugten", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "Hurtigskydende kanon, der kører med krudt.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ikke blokke, men gør stor skade.", "item.immersive_aircraft.telescope.description": "En mere omfangsrig version af kikkerten.", "item.immersive_aircraft.heavy_crossbow.description": "En tung armbrøst med et kraftigt slag, kræver pile.", "item.immersive_aircraft.item.upgrade": "Luftfartøjopgradering", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON> til fly", "item.immersive_aircraft.airship": "Luftskib", "item.immersive_aircraft.cargo_airship": "Luftskib til fragt", "item.immersive_aircraft.warship": "Krigsskib", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "Trædekopter", "item.immersive_aircraft.quadrocopter": "Quadrokopter", "item.immersive_aircraft.airship.description": "Luftskibe er måske ikke de hurtigste luftfartøjer, men de er nemme at manøvrere!", "item.immersive_aircraft.cargo_airship.description": "Langsom og brændstofsulten, men kan bære et helt lager.", "item.immersive_aircraft.warship.description": "En flyvende fæstning, lang<PERSON><PERSON>, men tungt bevæbnet.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON> og ret pålidelig. <PERSON><PERSON><PERSON> for, at din landingsbane er lang nok.", "item.immersive_aircraft.gyrodyne.description": "Hvem har brug for en motor, når man kan drive et luftfartøj med ren muskelkraft? Træd alt, hvad du orker, og flyv højt op i det blå!", "item.immersive_aircraft.quadrocopter.description": "Fire propeller spændt fast på en bambusramme – et mesterværk af ingeniørkunst! Perfekt til at bygge med, og det er det hele.", "immersive_aircraft.gyrodyne_target": "%d%% kraft – bliv ved med at træde!", "immersive_aircraft.gyrodyne_target_reached": "Klar til start!", "immersive_aircraft.invalid_dimension": "<PERSON><PERSON> fly fungerer ikke i denne dimension.", "immersive_aircraft.out_of_ammo": "Vi har ikke mere ammunition!", "immersive_aircraft.repair": "%s%% repareret!", "immersive_aircraft.tried_dismount": "Tryk igen for at springe ud!", "immersive_aircraft.fuel.none": "Det er ikke noget brændstof!", "immersive_aircraft.fuel.out": "Brændstoffet er løbet tør!", "immersive_aircraft.fuel.low": "Brændstofsniveauet er lavt!", "immersive_aircraft.fat.none": "Du har ikke mere energi!", "immersive_aircraft.fat.out": "<PERSON> orker ikke at blive ved med at træde!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON><PERSON> in<PERSON>", "option.immersive_aircraft.separateCamera": "B<PERSON> et andet kamera, når man er i et luftfartøj", "option.immersive_aircraft.useThirdPersonByDefault": "Brug tredjepersonsperspektiv, når man er i et luftfartøj", "option.immersive_aircraft.enableTrails": "Dampspor", "option.immersive_aircraft.enableAnimatedSails": "Blafrende sejl", "option.immersive_aircraft.renderDistance": "Synslængde i blokke", "option.immersive_aircraft.fuelConsumption": "Brændstofforbrændingshastighed", "option.immersive_aircraft.windClearWeather": "Vindpåvirkning i klart vejr", "option.immersive_aircraft.windRainWeather": "Vindpåvirkning under ne<PERSON><PERSON><PERSON>r", "option.immersive_aircraft.windThunderWeather": "Vindpåvirkning under tordenvejr", "option.immersive_aircraft.repairSpeed": "Reparation per klik.", "option.immersive_aircraft.repairExhaustion": "Udmattelse af spilleren pr. klik.", "option.immersive_aircraft.collisionDamage": "Kollisionsskade", "option.immersive_aircraft.burnFuelInCreative": "<PERSON><PERSON><PERSON><PERSON> brændstof i kreativ", "option.immersive_aircraft.acceptVanillaFuel": "Tillad normale brændstoffer (kul, træ, osv.)", "option.immersive_aircraft.useCustomKeybindSystem": "Brug flere tastebindinger (kan ødelægge nogle modifikationer)", "option.immersive_aircraft.showHotbarEngineGauge": "Gengiv motormåleren over hotbaren.", "option.immersive_aircraft.enableCrashExplosion": "Aktiver eksplosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktiver destruktiv eksplosion.", "option.immersive_aircraft.enableCrashFire": "Aktiver en brændende eksplosion.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON>sen af eksplosionen.", "option.immersive_aircraft.crashDamage": "Beskadig spilleren ved et styrt.", "option.immersive_aircraft.preventKillThroughCrash": "Vil ikke dræbe spilleren ved et crash.", "option.immersive_aircraft.healthBarRow": "Udligner køretøjernes helbredslinje.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON><PERSON> værdier gør flyet mere holdbart.", "option.immersive_aircraft.weaponsAreDestructive": "Lad nogle våben ødelægge blokke.", "option.immersive_aircraft.dropInventory": "Slip inventaret i stedet for at gemme det i flyelementet.", "option.immersive_aircraft.dropUpgrades": "Drop opgraderingerne og tilpasningen i stedet for at gemme dem i flyelementet.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenererer automatisk helbred for hvert givet tick og simulerer vanilla-adfærd.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON> k<PERSON>, <PERSON><PERSON><PERSON> du holder skiftet, ellers skal du bare gå ind i køretøjet.", "immersive_aircraft.tooltip.no_target": "Skal samles på gulvet!", "immersive_aircraft.tooltip.no_space": "Ikke nok plads!", "immersive_aircraft.slot.booster": "Hjælperaket", "immersive_aircraft.slot.weapon": "Våbenplads", "immersive_aircraft.slot.boiler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.banner": "Banner", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Opgradering", "immersive_aircraft.upgrade.enginespeed": "%s%% motoreffekt", "immersive_aircraft.upgrade.friction": "%s%% luftmodstand", "immersive_aircraft.upgrade.acceleration": "%s%% starthastighed", "immersive_aircraft.upgrade.durability": "%s%% holdbarhed", "immersive_aircraft.upgrade.fuel": "%s%% brændstofbehov", "immersive_aircraft.upgrade.wind": "%s%% vindpåvirkning", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliserende", "immersive_aircraft.tooltip.inventory": "Indeholder %s elementer."}