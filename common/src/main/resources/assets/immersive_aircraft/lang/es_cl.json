{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aviones inmersivos", "key.immersive_aircraft.multi_control_left": "Iz<PERSON>erda", "key.immersive_aircraft.multi_control_right": "A la derecha", "key.immersive_aircraft.multi_control_forward": "Adelante", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON> at<PERSON>", "key.immersive_aircraft.multi_control_up": "Arriba", "key.immersive_aircraft.multi_control_down": "Abajo", "key.immersive_aircraft.multi_control_pull": "Controlador de tracción", "key.immersive_aircraft.multi_control_push": "Controlador de empuje", "key.immersive_aircraft.multi_use": "Utilizar arma/montura", "key.immersive_aircraft.fallback_control_left": "Iz<PERSON>erda", "key.immersive_aircraft.fallback_control_right": "A la derecha", "key.immersive_aircraft.fallback_control_forward": "Adelante", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON> at<PERSON>", "key.immersive_aircraft.fallback_control_up": "Arriba", "key.immersive_aircraft.fallback_control_down": "Abajo", "key.immersive_aircraft.fallback_control_pull": "Controlador de tracción", "key.immersive_aircraft.fallback_control_push": "Controlador de empuje", "key.immersive_aircraft.fallback_use": "Utilizar arma/montura", "key.immersive_aircraft.dismount": "Desmonta", "key.immersive_aircraft.boost": "Impulso de cohete", "entity.immersive_aircraft.airship": "Dirigible", "entity.immersive_aircraft.cargo_airship": "Dirigible de carga", "entity.immersive_aircraft.warship": "Buque de guerra", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocóptero", "item.immersive_aircraft.hull": "Casco", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "Caldera", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON> me<PERSON>", "item.immersive_aircraft.eco_engine": "Motor ecológico", "item.immersive_aircraft.nether_engine": "Motor de Nether", "item.immersive_aircraft.steel_boiler": "Caldera de acero", "item.immersive_aircraft.industrial_gears": "Engranajes industriales", "item.immersive_aircraft.sturdy_pipes": "Tuberías resistentes", "item.immersive_aircraft.gyroscope": "Giroscopio", "item.immersive_aircraft.hull_reinforcement": "Refuerzo del casco", "item.immersive_aircraft.improved_landing_gear": "Tren de aterrizaje mejorado", "item.immersive_aircraft.rotary_cannon": "Cañón giratorio", "item.immersive_aircraft.bomb_bay": "Bahía de bombas", "item.immersive_aircraft.telescope": "Telescopio", "item.immersive_aircraft.heavy_crossbow": "Ballesta pesada", "item.immersive_aircraft.rotary_cannon.description": "Cañón de tiro rápido que funciona con pólvora.", "item.immersive_aircraft.bomb_bay.description": "Arroja TNT, no destruye bloques pero inflige mucho daño.", "item.immersive_aircraft.telescope.description": "Una versión más voluminosa del catalejo.", "item.immersive_aircraft.heavy_crossbow.description": "Una ballesta pesada con un potente golpe, requiere flechas.", "item.immersive_aircraft.item.upgrade": "Mejora del avión", "item.immersive_aircraft.item.weapon": "Arma Aérea", "item.immersive_aircraft.airship": "Dirigible", "item.immersive_aircraft.cargo_airship": "Dirigible de carga", "item.immersive_aircraft.warship": "Buque de guerra", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocóptero", "item.immersive_aircraft.airship.description": "<PERSON>uede que los dirigibles no sean el vehículo más rápido, pero son fáci<PERSON> de maniobrar.", "item.immersive_aircraft.cargo_airship.description": "Es lento y consume mucho combustible, pero transporta todo un almacén.", "item.immersive_aircraft.warship.description": "Una fortaleza voladora, lenta pero fuertemente armada.", "item.immersive_aircraft.biplane.description": "<PERSON>á<PERSON><PERSON> y bastante fiable. Asegúrate de que tu pista es lo suficientemente larga.", "item.immersive_aircraft.gyrodyne.description": "¿Quién necesita un motor si se puede propulsar el avión con pura fuerza? Dale un buen empujón y ¡a volar!", "item.immersive_aircraft.quadrocopter.description": "¡Una obra maestra de la ingeniería! 4 rotores atados a un poco de bambú. Perfecto para construir, y eso es todo.", "immersive_aircraft.gyrodyne_target": "%d%% ¡Potencia, sigue empujando!", "immersive_aircraft.gyrodyne_target_reached": "Velocidad mínima del rotor alcanzada, ¡listo para despegar!", "immersive_aircraft.invalid_dimension": "Este avión no funciona en esta dimensión.", "immersive_aircraft.out_of_ammo": "¡Sin munición!", "immersive_aircraft.repair": "%s%% ¡reparado!", "immersive_aircraft.tried_dismount": "¡Vuelve a pulsar para saltar!", "immersive_aircraft.fuel.none": "¡No hay combustible!", "immersive_aircraft.fuel.out": "¡Te has quedado sin combustible!", "immersive_aircraft.fuel.low": "¡Tienes poco combustible!", "immersive_aircraft.fat.none": "¡No hay comida!", "immersive_aircraft.fat.out": "¡Tienes demasiada hambre para volar!", "option.immersive_aircraft.general": "Opciones generales", "option.immersive_aircraft.separateCamera": "Utiliza una cámara independiente en el avión.", "option.immersive_aircraft.useThirdPersonByDefault": "<PERSON>r defecto, c<PERSON><PERSON> en tercera persona en el avión.", "option.immersive_aircraft.enableTrails": "Caminos de vapor elegantes.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON> on<PERSON>.", "option.immersive_aircraft.renderDistance": "Renderiza la distancia en bloques.", "option.immersive_aircraft.fuelConsumption": "Tasa de quema de combustible.", "option.immersive_aircraft.windClearWeather": "Efecto del viento base.", "option.immersive_aircraft.windRainWeather": "Viento en la precipitación.", "option.immersive_aircraft.windThunderWeather": "Viento extra en los truenos.", "option.immersive_aircraft.repairSpeed": "Reparación por clic.", "option.immersive_aircraft.repairExhaustion": "Agotamiento del jugador por clic.", "option.immersive_aircraft.collisionDamage": "Daños por colisión.", "option.immersive_aircraft.burnFuelInCreative": "Quema combustible en modo creativo.", "option.immersive_aircraft.acceptVanillaFuel": "Acepta el combustible de vainilla.", "option.immersive_aircraft.useCustomKeybindSystem": "U<PERSON><PERSON> enlaces multi-clave, puede romper ciertos mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderiza el indicador del motor sobre la barra caliente.", "option.immersive_aircraft.enableCrashExplosion": "Activar explosión.", "option.immersive_aircraft.enableCrashBlockDestruction": "Activa la explosión destructiva.", "option.immersive_aircraft.enableCrashFire": "Habilita la explosión ardiente.", "option.immersive_aircraft.crashExplosionRadius": "Tamaño de la explosión del choque.", "option.immersive_aircraft.crashDamage": "Daña al jugador en un choque.", "option.immersive_aircraft.preventKillThroughCrash": "No matará al jugador en caso de colisión.", "option.immersive_aircraft.healthBarRow": "Desplaza la barra de salud de los vehículos.", "option.immersive_aircraft.damagePerHealthPoint": "Los valores más altos hacen que los aviones sean más duraderos.", "option.immersive_aircraft.weaponsAreDestructive": "Permite que algunas armas destruyan bloques.", "option.immersive_aircraft.dropInventory": "Suelta el inventario, en lugar de guardarlo dentro del elemento de avión.", "option.immersive_aircraft.dropUpgrades": "Suelta las mejoras y la personalización, en lugar de guardarla dentro del elemento aeronave.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenera automáticamente la salud cada cierto tiempo, simulando el comportamiento vanilla.", "option.immersive_aircraft.requireShiftForRepair": "Solo reparar al mantener shift, de lo contrario solo entra en el vehículo.", "immersive_aircraft.tooltip.no_target": "¡Hay que montarlo en el suelo!", "immersive_aircraft.tooltip.no_space": "¡No hay espacio suficiente!", "immersive_aircraft.slot.booster": "Cohetes propulsores", "immersive_aircraft.slot.weapon": "Ra<PERSON>ra para armas", "immersive_aircraft.slot.boiler": "Ranura de combustible", "immersive_aircraft.slot.banner": "<PERSON> ranura", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON> de tinte", "immersive_aircraft.slot.upgrade": "<PERSON><PERSON><PERSON> de mejora", "immersive_aircraft.upgrade.enginespeed": "%s%% potencia del motor", "immersive_aircraft.upgrade.friction": "%s%% rozamiento del aire", "immersive_aircraft.upgrade.acceleration": "%s%% velocidad de despegue", "immersive_aircraft.upgrade.durability": "%s%% durabilidad", "immersive_aircraft.upgrade.fuel": "%s%% necesidad de combustible", "immersive_aircraft.upgrade.wind": "%s%% efecto del viento", "immersive_aircraft.upgrade.stabilizer": "%s%% estabilizando", "immersive_aircraft.tooltip.inventory": "Contiene %s artículos."}