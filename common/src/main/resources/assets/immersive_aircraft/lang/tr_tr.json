{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Sürükleyici Uçaklar", "key.immersive_aircraft.multi_control_left": "Sol", "key.immersive_aircraft.multi_control_right": "Sağ", "key.immersive_aircraft.multi_control_forward": "İleri", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Yukarı", "key.immersive_aircraft.multi_control_down": "Aşağı", "key.immersive_aircraft.multi_control_pull": "Çekme Kontrolörü", "key.immersive_aircraft.multi_control_push": "İtme Kontrolörü", "key.immersive_aircraft.multi_use": "<PERSON><PERSON>/binek kullan", "key.immersive_aircraft.fallback_control_left": "Sol", "key.immersive_aircraft.fallback_control_right": "Sağ", "key.immersive_aircraft.fallback_control_forward": "İleri", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Yukarı", "key.immersive_aircraft.fallback_control_down": "Aşağı", "key.immersive_aircraft.fallback_control_pull": "Çekme Kontrolörü", "key.immersive_aircraft.fallback_control_push": "İtme Kontrolörü", "key.immersive_aircraft.fallback_use": "<PERSON><PERSON>/binek kullan", "key.immersive_aircraft.dismount": "İn", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON> des<PERSON>ği", "entity.immersive_aircraft.airship": "<PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON>", "entity.immersive_aircraft.warship": "Savaş Gemisi", "entity.immersive_aircraft.biplane": "Çift Kanatlı", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Pervane", "item.immersive_aircraft.boiler": "Kazan", "item.immersive_aircraft.enhanced_propeller": "Geliştirilmiş <PERSON>", "item.immersive_aircraft.eco_engine": "Eko Motor", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON>u", "item.immersive_aircraft.steel_boiler": "Çelik Kazan", "item.immersive_aircraft.industrial_gears": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.sturdy_pipes": "Sağlam Borular", "item.immersive_aircraft.gyroscope": "Jiroskop", "item.immersive_aircraft.hull_reinforcement": "<PERSON><PERSON>v<PERSON>", "item.immersive_aircraft.improved_landing_gear": "Geliştirilmiş İniş Takımları", "item.immersive_aircraft.rotary_cannon": "Döner Top", "item.immersive_aircraft.bomb_bay": "<PERSON><PERSON>", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "Barutla çalışan hızlı ateş eden top.", "item.immersive_aircraft.bomb_bay.description": "TNT düşürür, blokları yok etmez ancak ağır hasar verir.", "item.immersive_aircraft.telescope.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> daha hantal bir versiyonu.", "item.immersive_aircraft.heavy_crossbow.description": "Güçlü bir darbeye sahip ağır bir arbalet, ok gerektirir.", "item.immersive_aircraft.item.upgrade": "Uçak Yükseltmesi", "item.immersive_aircraft.item.weapon": "Hava Aracı Silahı", "item.immersive_aircraft.airship": "<PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON>", "item.immersive_aircraft.warship": "Savaş Gemisi", "item.immersive_aircraft.biplane": "Çift Kanatlı", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Hava gemileri en hızlı araç olmayabilir, ancak manevra yapmaları kesinlikle kolaydır.", "item.immersive_aircraft.cargo_airship.description": "Yavaş ve yakıta aç ama tüm depoyu taşıyor.", "item.immersive_aircraft.warship.description": "Uçan bir kale, yava<PERSON> ama ağır <PERSON>.", "item.immersive_aircraft.biplane.description": "Hızlı ve oldukça güvenilir. Pistinizin yeterince uzun olduğundan emin olun.", "item.immersive_aircraft.gyrodyne.description": "Uçağa saf kas gü<PERSON>ü<PERSON> güç verilebiliyorsa motora ne gerek var? İyi bir itme verin ve uçsun!", "item.immersive_aircraft.quadrocopter.description": "Bir mühendislik şaheseri! Bambuya bağlanmış 4 rotor. İnşa etmek için mü<PERSON>l, hepsi bu.", "immersive_aircraft.gyrodyne_target": "%dG<PERSON><PERSON>, itmeye devam et!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotor hızına ula<PERSON>ıldı, kalkışa hazır!", "immersive_aircraft.invalid_dimension": "Bu uçak bu boyutta çalışmıyor.", "immersive_aircraft.out_of_ammo": "<PERSON><PERSON><PERSON><PERSON> bitti!", "immersive_aircraft.repair": "%s% tamir edildi!", "immersive_aircraft.tried_dismount": "Atlamak için tekrar basın!", "immersive_aircraft.fuel.none": "Yakıt yok!", "immersive_aircraft.fuel.out": "<PERSON><PERSON><PERSON><PERSON><PERSON>n bitti!", "immersive_aircraft.fuel.low": "Yakıtınız azaldı!", "immersive_aircraft.fat.none": "Yemek yok!", "immersive_aircraft.fat.out": "Uçamayacak kadar açsın!", "option.immersive_aircraft.general": "<PERSON><PERSON>", "option.immersive_aircraft.separateCamera": "Uçakta ayrı bir kamera kullanın.", "option.immersive_aircraft.useThirdPersonByDefault": "Uçakta varsayılan olarak üçüncü şahıs kamerası kullanılır.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON><PERSON><PERSON><PERSON> buhar yolları.", "option.immersive_aircraft.enableAnimatedSails": "Dalgal<PERSON> yelkenler.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> blo<PERSON>r halind<PERSON>.", "option.immersive_aircraft.fuelConsumption": "Yakıt yakma oranı.", "option.immersive_aircraft.windClearWeather": "<PERSON><PERSON> rü<PERSON>.", "option.immersive_aircraft.windRainWeather": "Yağışta rüzgar.", "option.immersive_aircraft.windThunderWeather": "Gök gürültüsünde ekstra rüzgar.", "option.immersive_aircraft.repairSpeed": "Tıklama başına onarım.", "option.immersive_aircraft.repairExhaustion": "Tıklama başına oyuncu yorgunluğu.", "option.immersive_aircraft.collisionDamage": "Çarpışma hasarı.", "option.immersive_aircraft.burnFuelInCreative": "Yaratıcı modda yakıt yakın.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON><PERSON> yakıtını kabul edin.", "option.immersive_aircraft.useCustomKeybindSystem": "Çoklu keybindings kull<PERSON><PERSON>n, bazı modları bozabilir.", "option.immersive_aircraft.showHotbarEngineGauge": "Sıcak çubuğun üzerindeki motor göstergesini oluşturun.", "option.immersive_aircraft.enableCrashExplosion": "Patlamayı etkinleştirin.", "option.immersive_aircraft.enableCrashBlockDestruction": "Yıkıcı patlamayı etkinleştirin.", "option.immersive_aircraft.enableCrashFire": "Ateşli patlamayı etkinleştirin.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON>n boyutu.", "option.immersive_aircraft.crashDamage": "Bir çarpışmada oyuncuya hasar verin.", "option.immersive_aircraft.preventKillThroughCrash": "Bir çarpışmada oyuncuyu öldürmez.", "option.immersive_aircraft.healthBarRow": "Araçların sağlık çubuğunu dengeleyin.", "option.immersive_aircraft.damagePerHealthPoint": "Daha yüksek değerler uçakları daha dayanıklı hale getirir.", "option.immersive_aircraft.weaponsAreDestructive": "Bazı silahların blokları yok etmesine izin verin.", "option.immersive_aircraft.dropInventory": "Envanteri uçak öğesi içinde kaydetmek yerine bırakın.", "option.immersive_aircraft.dropUpgrades": "Yükseltmeleri ve özelleştirmeyi uçak öğesi içinde kaydetmek yerine bırakın.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Geçen her birim zamanda sa<PERSON> yeniler, vanilla dav<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> simüle eder.", "option.immersive_aircraft.requireShiftForRepair": "Yalnızca shift tuşuna basarken onar, <PERSON><PERSON> de<PERSON> araca gir.", "immersive_aircraft.tooltip.no_target": "Zeminde birleştirilmesi gerekiyor!", "immersive_aircraft.tooltip.no_space": "<PERSON><PERSON><PERSON> alan yok!", "immersive_aircraft.slot.booster": "Yüksel<PERSON>me r<PERSON>", "immersive_aircraft.slot.weapon": "<PERSON><PERSON> yu<PERSON>ı", "immersive_aircraft.slot.boiler": "Yakıt yuvası", "immersive_aircraft.slot.banner": "Banner yuvası", "immersive_aircraft.slot.dye": "Boya yuvası", "immersive_aircraft.slot.upgrade": "Yükseltme yuvası", "immersive_aircraft.upgrade.enginespeed": "%s%% motor gücü", "immersive_aircraft.upgrade.friction": "%s%% hava sürtünmesi", "immersive_aircraft.upgrade.acceleration": "%s%% kalkış hızı", "immersive_aircraft.upgrade.durability": "%s%% dayanıklılık", "immersive_aircraft.upgrade.fuel": "%s%% yakıt ihtiyacı", "immersive_aircraft.upgrade.wind": "%s%% rüzgar etkisi", "immersive_aircraft.upgrade.stabilizer": "%s% stabilize", "immersive_aircraft.tooltip.inventory": "%s öğelerini içerir."}