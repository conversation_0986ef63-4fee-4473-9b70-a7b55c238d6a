{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersiv Fligzeug", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Ob", "key.immersive_aircraft.multi_control_down": "Ënnen", "key.immersive_aircraft.multi_control_pull": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_push": "Drécken Controller", "key.immersive_aircraft.multi_use": "Benotzt Waff/ méi benotzen", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Ob", "key.immersive_aircraft.fallback_control_down": "Ënnen", "key.immersive_aircraft.fallback_control_pull": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_push": "Drécken Controller", "key.immersive_aircraft.fallback_use": "Benotzt Waff/ méi benotzen", "key.immersive_aircraft.dismount": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "Cargo Airship", "entity.immersive_aircraft.warship": "Kriegsschëff", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Kessel", "item.immersive_aircraft.enhanced_propeller": "Verstärkt Propeller", "item.immersive_aircraft.eco_engine": "Eco Motor", "item.immersive_aircraft.nether_engine": "Nether Motor", "item.immersive_aircraft.steel_boiler": "Stahl Kessel", "item.immersive_aircraft.industrial_gears": "Industriell Gears", "item.immersive_aircraft.sturdy_pipes": "Stécker Pipes", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Hull Verstäerkung", "item.immersive_aircraft.improved_landing_gear": "Verbesserte Landungsgear", "item.immersive_aircraft.rotary_cannon": "Rotary Cannon", "item.immersive_aircraft.bomb_bay": "Bomm Bay", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Schweri Viller", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON>uer Kanon déi mat <PERSON>.", "item.immersive_aircraft.bomb_bay.description": "Fällt TNT, zerstéiert keng Blokker awer mécht schwéier<PERSON> Schued.", "item.immersive_aircraft.telescope.description": "Eng méi déck Versioun vum Spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "Eng schwerschweri Viller mat engem mächtegen Effekt, brauch Pfeile.", "item.immersive_aircraft.item.upgrade": "Mënsch Upgrade", "item.immersive_aircraft.item.weapon": "Aeronautik Waff", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "Cargo Airship", "item.immersive_aircraft.warship": "Kriegsschëff", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lang<PERSON><PERSON> an Tre<PERSON> hungrig, mee transportéiert eng ganz <PERSON>ger.", "item.immersive_aircraft.warship.description": "E fléien Fortress, lues awer schw<PERSON>ier bewapnet.", "item.immersive_aircraft.biplane.description": "Schnell a recht zouverlässeg. <PERSON><PERSON><PERSON>, datt Är Landebahn lang genuch ass.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Kraft, weitermachen!", "immersive_aircraft.gyrodyne_target_reached": "Minimal Rotor Geschwindegkeet erreecht, bereet fir d'<PERSON><PERSON><PERSON>!", "immersive_aircraft.invalid_dimension": "Dës Aeronautik funktionnéiert net a dëser Dimensioun.", "immersive_aircraft.out_of_ammo": "<PERSON><PERSON>!", "immersive_aircraft.repair": "%s%% reparéiert!", "immersive_aircraft.tried_dismount": "Dr<PERSON>it nach eng Kéier fir eraus ze sprangen!", "immersive_aircraft.fuel.none": "Keen Brennstoff!", "immersive_aircraft.fuel.out": "Dir hutt den Brennstoff ausgegraff!", "immersive_aircraft.fuel.low": "Dir sidd niddereg am Brennstoff!", "immersive_aircraft.fat.none": "<PERSON>en <PERSON>!", "immersive_aircraft.fat.out": "Dir sidd ze hungrig fir ze fléien!", "option.immersive_aircraft.general": "Allgemeng <PERSON>", "option.immersive_aircraft.separateCamera": "Benotzt eng separat Kamera am Mënsch.", "option.immersive_aircraft.useThirdPersonByDefault": "Standard fir drëtt Persoun Kamera am Mënsch.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON>puren.", "option.immersive_aircraft.enableAnimatedSails": "Welle Sails.", "option.immersive_aircraft.renderDistance": "Render Distanz an Blöcker.", "option.immersive_aircraft.fuelConsumption": "Brennstoffverbrauch.", "option.immersive_aircraft.windClearWeather": "Basis Wand Effekt.", "option.immersive_aircraft.windRainWeather": "<PERSON>d beim <PERSON>.", "option.immersive_aircraft.windThunderWeather": "Extra Wand beim <PERSON>.", "option.immersive_aircraft.repairSpeed": "Reparéieren pro Klick.", "option.immersive_aircraft.repairExhaustion": "Spiller Erschöpfung pro Klick.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "Brenn Brennstoff am kreativen Modus.", "option.immersive_aircraft.acceptVanillaFuel": "Ak<PERSON><PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "Benotzt Multi-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kann gewëssen Mods briechen.", "option.immersive_aircraft.showHotbarEngineGauge": "Render d'Motor gauge iwwer der Hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Aktivéiert d'Explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktivéiert destruktiv Explosion.", "option.immersive_aircraft.enableCrashFire": "Aktivéiert brennend Explosion.", "option.immersive_aircraft.crashExplosionRadius": "Gréisst vun der Crash-Explosion.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON><PERSON> bei engem Crash.", "option.immersive_aircraft.preventKillThroughCrash": "<PERSON><PERSON>sen Crash wäert den Spiller net ëmbréngen.", "option.immersive_aircraft.healthBarRow": "Offset d'Gesondheetsbar vun <PERSON>.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON>er maachen d'Aeronautik méi resistent.", "option.immersive_aircraft.weaponsAreDestructive": "Erlaabt e puer Wapen fir Blokke ze zerstéieren.", "option.immersive_aircraft.dropInventory": "Schéckt d'Inventar erof, amplaz et an der Luchtmäßel ze späicheren.", "option.immersive_aircraft.dropUpgrades": "Schéckt d'Upgrade an d'Personalisatioun, amplaz et an der Luchtmäßel ze späicheren.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneréiert automatesch d'Gesondheet all gegebene <PERSON>, wa simuléiert d'Verhalen aus der Vanilla.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON><PERSON> ré<PERSON>er wann d'Shift ged<PERSON>it ass, soss einfach an d'Fuerzeug eintrieden.", "immersive_aircraft.tooltip.no_target": "Muss um Buedem assembléiert ginn!", "immersive_aircraft.tooltip.no_space": "Net genuch Plaz!", "immersive_aircraft.slot.booster": "<PERSON><PERSON>", "immersive_aircraft.slot.weapon": "Waffen Slot", "immersive_aircraft.slot.boiler": "Brennstoff Slot", "immersive_aircraft.slot.banner": "Banner Slot", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Upgrade Slot", "immersive_aircraft.upgrade.enginespeed": "%s%% Motor Kraaft", "immersive_aircraft.upgrade.friction": "%s%% Loft Reibung", "immersive_aircraft.upgrade.acceleration": "%s%% Start Geschwindegkeet", "immersive_aircraft.upgrade.durability": "%s%% Haltbarkeet", "immersive_aircraft.upgrade.fuel": "%s%% Brennstoff Ufuerderung", "immersive_aircraft.upgrade.wind": "%s%% Wand Effekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliséierend", "immersive_aircraft.tooltip.inventory": "Enthält %s Elementer."}