{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "veSqa' jengva<PERSON>", "key.immersive_aircraft.multi_control_left": "mIHlI'", "key.immersive_aircraft.multi_control_right": "puj", "key.immersive_aircraft.multi_control_forward": "b<PERSON><PERSON>'", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON><PERSON> mInDu'", "key.immersive_aircraft.multi_control_up": "nob", "key.immersive_aircraft.multi_control_down": "bIjaH", "key.immersive_aircraft.multi_control_pull": "ghuH Controller", "key.immersive_aircraft.multi_control_push": "chIm Controller", "key.immersive_aircraft.multi_use": "veS/choHta'", "key.immersive_aircraft.fallback_control_left": "mIHlI'", "key.immersive_aircraft.fallback_control_right": "puj", "key.immersive_aircraft.fallback_control_forward": "b<PERSON><PERSON>'", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON><PERSON> mInDu'", "key.immersive_aircraft.fallback_control_up": "nob", "key.immersive_aircraft.fallback_control_down": "bIjaH", "key.immersive_aircraft.fallback_control_pull": "ghuH Controller", "key.immersive_aircraft.fallback_control_push": "chIm Controller", "key.immersive_aircraft.fallback_use": "veS/choHta'", "key.immersive_aircraft.dismount": "ghu<PERSON> chIm", "key.immersive_aircraft.boost": "qeH boost", "entity.immersive_aircraft.airship": "chirgh", "entity.immersive_aircraft.cargo_airship": "vInuSna' Qe'nIgh", "entity.immersive_aircraft.warship": "roD", "entity.immersive_aircraft.biplane": "bIpla’n", "entity.immersive_aircraft.gyrodyne": "gyrodyne", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON>", "item.immersive_aircraft.hull": "naH", "item.immersive_aircraft.engine": "bIQ'a'", "item.immersive_aircraft.sail": "\"tIgh\"", "item.immersive_aircraft.propeller": "\"je\"", "item.immersive_aircraft.boiler": "boiler", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON> luj", "item.immersive_aircraft.eco_engine": "Eco veS", "item.immersive_aircraft.nether_engine": "nether veS", "item.immersive_aircraft.steel_boiler": "stee<PERSON>mey", "item.immersive_aircraft.industrial_gears": "Industrial gears", "item.immersive_aircraft.sturdy_pipes": "nIgh puqloD", "item.immersive_aircraft.gyroscope": "gyroscope", "item.immersive_aircraft.hull_reinforcement": "nuqneH ghuS", "item.immersive_aircraft.improved_landing_gear": "bIghoS 'ej ghu<PERSON>", "item.immersive_aircraft.rotary_cannon": "vaSpu'", "item.immersive_aircraft.bomb_bay": "bI'ren", "item.immersive_aircraft.telescope": "mInHa'", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "pagh qej vIDel", "item.immersive_aircraft.bomb_bay.description": "TNT vIj; 'ach naDev 'uy pagh vIdan.", "item.immersive_aircraft.telescope.description": "p<PERSON> 'u<PERSON> '<PERSON><PERSON>", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>lo'qur, ghIH mI' veS.", "item.immersive_aircraft.item.upgrade": "<PERSON><PERSON><PERSON><PERSON>'wI' nga'", "item.immersive_aircraft.item.weapon": "vInuSna' veS", "item.immersive_aircraft.airship": "chirgh", "item.immersive_aircraft.cargo_airship": "vInuSna' Qe'nIgh", "item.immersive_aircraft.warship": "roD", "item.immersive_aircraft.biplane": "bIpla’n", "item.immersive_aircraft.gyrodyne": "gyrodyne", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "poSmoHtaH 'ej toHpa' 'oH 'ej 'uy' tIq chenmoHHa'", "item.immersive_aircraft.warship.description": "mInDu'lIj je naQHommey, nuHmey retlhbogh 'ej 'uQmoH law' latlh.", "item.immersive_aircraft.biplane.description": "wIv 'ej peSlaH, ghoSmoH 'ej 'uy' tIq wIv", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% boQ<PERSON>y, DuQ!", "immersive_aircraft.gyrodyne_target_reached": "nItlhu' rotor bobte' vIghoS!", "immersive_aircraft.invalid_dimension": "vInuSna' vIqDaq 'oHbe'", "immersive_aircraft.out_of_ammo": "ay'be'!", "immersive_aircraft.repair": "%s%% pIm!", "immersive_aircraft.tried_dismount": "naDev vIjeH!", "immersive_aircraft.fuel.none": "mIHlI' quvbej!", "immersive_aircraft.fuel.out": "mIHlI' puqloD!", "immersive_aircraft.fuel.low": "mIgh pa'lo'!", "immersive_aircraft.fat.none": "mIHlI' yIneH!", "immersive_aircraft.fat.out": "tlhamDaq DujDaq quv Duj!", "option.immersive_aircraft.general": "mInDu' nga'", "option.immersive_aircraft.separateCamera": "<PERSON><PERSON><PERSON><PERSON> boqroH latlh cameras lo'.", "option.immersive_aircraft.useThirdPersonByDefault": "SorDujDaq chImtaH Hol qengmoH.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON><PERSON><PERSON><PERSON> quj.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>.", "option.immersive_aircraft.renderDistance": "Render Daqmey lo'.", "option.immersive_aircraft.fuelConsumption": "qutI' ghojmoH.", "option.immersive_aircraft.windClearWeather": "yIqel wIjeH.", "option.immersive_aircraft.windRainWeather": "mInDu' value'e'.", "option.immersive_aircraft.windThunderWeather": "bIghoS 'ej yesterday'e'.", "option.immersive_aircraft.repairSpeed": "nI' naD", "option.immersive_aircraft.repairExhaustion": "naDev 'o<PERSON> <PERSON><PERSON>j vIqargh ghoSna'Daq.", "option.immersive_aircraft.collisionDamage": "pagh lujchu'.", "option.immersive_aircraft.burnFuelInCreative": "mIgh egh qaj chaH chaH.", "option.immersive_aircraft.acceptVanillaFuel": "vaQveS qu' ghaH.", "option.immersive_aircraft.useCustomKeybindSystem": "roH loQ motlh, maqop wIghoSbe'.", "option.immersive_aircraft.showHotbarEngineGauge": "veS tlha' qaDpa','", "option.immersive_aircraft.enableCrashExplosion": "qet jIH", "option.immersive_aircraft.enableCrashBlockDestruction": "qet 'u'eb chaH", "option.immersive_aircraft.enableCrashFire": "qet 'ov", "option.immersive_aircraft.crashExplosionRadius": "qet jIH", "option.immersive_aircraft.crashDamage": "jIHvaD SuH YIQChugh", "option.immersive_aircraft.preventKillThroughCrash": "vIHbe' jIHvaD", "option.immersive_aircraft.healthBarRow": "yIH Dolu' bejatlh", "option.immersive_aircraft.damagePerHealthPoint": "chaH 'u' yIH, 'uy' ghor", "option.immersive_aircraft.weaponsAreDestructive": "puH 'oH 'ach yoSmeH naHmey vIje' vIneH.", "option.immersive_aircraft.dropInventory": "puH HaD 'oH neH, Hegh vIneH 'e' vIje' je.", "option.immersive_aircraft.dropUpgrades": "puH 'oH je, <PERSON>gh retlh<PERSON>gh 'e' vIneH 'e' vIje' je.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "naHmey HIja'vIS vIghuHmeH tlhej vIghuH 'ej vIghoS naHmey-centric lifestyles.", "option.immersive_aircraft.requireShiftForRepair": "vam naHmey vIwIv 'oH jatlhtaH, 'ach legh vIneH 'e' vIje'.", "immersive_aircraft.tooltip.no_target": "Qo' neH 'oH!", "immersive_aircraft.tooltip.no_space": "yuQwIj vIghuHbe'!", "immersive_aircraft.slot.booster": "Dubu'pu'", "immersive_aircraft.slot.weapon": "veS bI'ren", "immersive_aircraft.slot.boiler": "je<PERSON><PERSON><PERSON> puqlo<PERSON>", "immersive_aircraft.slot.banner": "venH puqloD", "immersive_aircraft.slot.dye": "Dye puqloD", "immersive_aircraft.slot.upgrade": "qutI' puqloD", "immersive_aircraft.upgrade.enginespeed": "%s%% veS naQ", "immersive_aircraft.upgrade.friction": "%s%% ghajo'q", "immersive_aircraft.upgrade.acceleration": "%s%% bujDaq sou", "immersive_aircraft.upgrade.durability": "%s%% lIgh", "immersive_aircraft.upgrade.fuel": "%s%% loS", "immersive_aircraft.upgrade.wind": "%s%% TegH", "immersive_aircraft.upgrade.stabilizer": "%s%% pIch", "immersive_aircraft.tooltip.inventory": "%s naQmey tu'lu'."}