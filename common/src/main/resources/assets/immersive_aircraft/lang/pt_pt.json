{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aeronaves imersivas", "key.immersive_aircraft.multi_control_left": "E<PERSON>rda", "key.immersive_aircraft.multi_control_right": "Certo", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Para trás", "key.immersive_aircraft.multi_control_up": "Para cima", "key.immersive_aircraft.multi_control_down": "Para baixo", "key.immersive_aircraft.multi_control_pull": "Controlador de Puxar", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Usa arma/montagem", "key.immersive_aircraft.fallback_control_left": "E<PERSON>rda", "key.immersive_aircraft.fallback_control_right": "Certo", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Para trás", "key.immersive_aircraft.fallback_control_up": "Para cima", "key.immersive_aircraft.fallback_control_down": "Para baixo", "key.immersive_aircraft.fallback_control_pull": "Controlador de Puxar", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Usa arma/montagem", "key.immersive_aircraft.dismount": "Desmontar", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "Aeronave", "entity.immersive_aircraft.cargo_airship": "Aeróstato de carga", "entity.immersive_aircraft.warship": "Navio <PERSON>", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Casco", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "<PERSON>deira", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.eco_engine": "Motor Ecológico", "item.immersive_aircraft.nether_engine": "Motor Nether", "item.immersive_aircraft.steel_boiler": "Caldeira de aço", "item.immersive_aircraft.industrial_gears": "Artes industriais", "item.immersive_aircraft.sturdy_pipes": "Tubos robustos", "item.immersive_aircraft.gyroscope": "Giroscópio", "item.immersive_aircraft.hull_reinforcement": "Reforço do casco", "item.immersive_aircraft.improved_landing_gear": "Trem de aterragem melhorado", "item.immersive_aircraft.rotary_cannon": "Canhão rotativo", "item.immersive_aircraft.bomb_bay": "Baía da bomba", "item.immersive_aircraft.telescope": "Telescópio", "item.immersive_aircraft.heavy_crossbow": "Besta pesada", "item.immersive_aircraft.rotary_cannon.description": "Canhão de disparo rápido que funciona com pólvora.", "item.immersive_aircraft.bomb_bay.description": "Deixa cair TNT, não destrói blocos mas causa grandes danos.", "item.immersive_aircraft.telescope.description": "Uma versão mais volumosa do óculo.", "item.immersive_aircraft.heavy_crossbow.description": "Uma besta pesada com um soco poderoso, precisa de flechas.", "item.immersive_aircraft.item.upgrade": "Upgrade de Aeronaves", "item.immersive_aircraft.item.weapon": "Arma da aeronave", "item.immersive_aircraft.airship": "Aeronave", "item.immersive_aircraft.cargo_airship": "Aeróstato de carga", "item.immersive_aircraft.warship": "Navio <PERSON>", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Os dirigíveis podem não ser o veículo mais r<PERSON>, mas são certamente fáceis de manobrar.", "item.immersive_aircraft.cargo_airship.description": "É lento e consome muito combustível, mas transporta todo um armazém.", "item.immersive_aircraft.warship.description": "Uma fortaleza voadora, lenta mas fortemente armada.", "item.immersive_aircraft.biplane.description": "Rápido e bastante fi<PERSON>. Certifique-se de que a sua pista é suficientemente longa.", "item.immersive_aircraft.gyrodyne.description": "Quem precisa de um motor se se pode alimentar a aeronave com músculos puros? Dêem-lhe um bom empurrão e saiam voando!", "item.immersive_aircraft.quadrocopter.description": "Uma obra-prima de engenharia! 4 rotores amarrados a algum bambu. Perfeito para a construção, e é só isso.", "immersive_aircraft.gyrodyne_target": "%d% Potência, continue a empurrar!", "immersive_aircraft.gyrodyne_target_reached": "Velocidade mínima do rotor alcançada, pronto para a descolagem!", "immersive_aircraft.invalid_dimension": "Este avião não funciona nesta dimensão.", "immersive_aircraft.out_of_ammo": "Não tens munições!", "immersive_aircraft.repair": "%s%% reparado!", "immersive_aircraft.tried_dismount": "Carrega novamente para saltar!", "immersive_aircraft.fuel.none": "Sem combustível!", "immersive_aircraft.fuel.out": "Ficou sem combustível!", "immersive_aircraft.fuel.low": "Tem pouco combustível!", "immersive_aircraft.fat.none": "Sem comida!", "immersive_aircraft.fat.out": "Está com demasiada fome para voar!", "option.immersive_aircraft.general": "Opções Gerais", "option.immersive_aircraft.separateCamera": "Utilizar uma câmara fotográfica separada nos aviões.", "option.immersive_aircraft.useThirdPersonByDefault": "O defeito é a câmara de terceira pessoa em aviões.", "option.immersive_aircraft.enableTrails": "Pistas de vapor extravagantes.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON> on<PERSON>.", "option.immersive_aircraft.renderDistance": "Distância de entrega em blocos.", "option.immersive_aircraft.fuelConsumption": "Taxa de queima de combustível.", "option.immersive_aircraft.windClearWeather": "Efeito do vento de base.", "option.immersive_aircraft.windRainWeather": "Vento ao cair da chuva.", "option.immersive_aircraft.windThunderWeather": "Vento extra ao trovão.", "option.immersive_aircraft.repairSpeed": "Reparação por clique.", "option.immersive_aircraft.repairExhaustion": "Exaustão do jogador por clique.", "option.immersive_aircraft.collisionDamage": "Danos por colisão.", "option.immersive_aircraft.burnFuelInCreative": "Queimar combustível em modo criativo.", "option.immersive_aircraft.acceptVanillaFuel": "Aceitar combustível de baunilha.", "option.immersive_aircraft.useCustomKeybindSystem": "Utilizar ligações multi-keybindings, pode quebrar certos mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderiza o indicador do motor sobre a hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Permite a explosão.", "option.immersive_aircraft.enableCrashBlockDestruction": "Permite uma explosão destrutiva.", "option.immersive_aircraft.enableCrashFire": "Permite a explosão de fogo.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON> da explosão do acidente.", "option.immersive_aircraft.crashDamage": "Danificar o jogador num acidente.", "option.immersive_aircraft.preventKillThroughCrash": "Não mata o jogador em caso de colisão.", "option.immersive_aircraft.healthBarRow": "Compensa a barra de saúde dos veículos.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON> mais elevados tornam as aeronaves mais duradouras.", "option.immersive_aircraft.weaponsAreDestructive": "Permite que algumas armas destruam blocos.", "option.immersive_aircraft.dropInventory": "Larga o inventário, em vez de o guardares no item da aeronave.", "option.immersive_aircraft.dropUpgrades": "<PERSON><PERSON><PERSON> cair as actualizações e a personalização, em vez de as guardar no item da aeronave.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenera automaticamente a vida a cada tick, simulando o comportamento do jogo.", "option.immersive_aircraft.requireShiftForRepair": "Repara apenas se mantiveres a mudança de velocidades, caso contrário, entra no veículo.", "immersive_aircraft.tooltip.no_target": "Nec<PERSON><PERSON> de ser montado no chão!", "immersive_aircraft.tooltip.no_space": "Não tens espaço suficiente!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.weapon": "<PERSON><PERSON><PERSON> da arma", "immersive_aircraft.slot.boiler": "Ranhura de combustível", "immersive_aircraft.slot.banner": "Ranhura da bandeira", "immersive_aircraft.slot.dye": "Ra<PERSON><PERSON> para tingir", "immersive_aircraft.slot.upgrade": "Ranhura de actualização", "immersive_aircraft.upgrade.enginespeed": "%s%% potência do motor", "immersive_aircraft.upgrade.friction": "%s%% fricção de ar", "immersive_aircraft.upgrade.acceleration": "%s%% velocidade de descolagem", "immersive_aircraft.upgrade.durability": "%s%% durabilidade", "immersive_aircraft.upgrade.fuel": "%s%% necessidade de combustível", "immersive_aircraft.upgrade.wind": "%s%% efeito do vento", "immersive_aircraft.upgrade.stabilizer": "%s%% estabilização", "immersive_aircraft.tooltip.inventory": "Contém %s itens."}