{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "身歷其境的飛機", "key.immersive_aircraft.multi_control_left": "左邊", "key.immersive_aircraft.multi_control_right": "對", "key.immersive_aircraft.multi_control_forward": "前進", "key.immersive_aircraft.multi_control_backward": "向後", "key.immersive_aircraft.multi_control_up": "向上", "key.immersive_aircraft.multi_control_down": "下降", "key.immersive_aircraft.multi_control_pull": "拉力控制器", "key.immersive_aircraft.multi_control_push": "按鈕控制器", "key.immersive_aircraft.multi_use": "使用武器/掛載", "key.immersive_aircraft.fallback_control_left": "左邊", "key.immersive_aircraft.fallback_control_right": "對", "key.immersive_aircraft.fallback_control_forward": "前進", "key.immersive_aircraft.fallback_control_backward": "向後", "key.immersive_aircraft.fallback_control_up": "向上", "key.immersive_aircraft.fallback_control_down": "下降", "key.immersive_aircraft.fallback_control_pull": "拉力控制器", "key.immersive_aircraft.fallback_control_push": "按鈕控制器", "key.immersive_aircraft.fallback_use": "使用武器/掛載", "key.immersive_aircraft.dismount": "下馬", "key.immersive_aircraft.boost": "火箭助推", "entity.immersive_aircraft.airship": "飛艇", "entity.immersive_aircraft.cargo_airship": "貨運飛船", "entity.immersive_aircraft.warship": "軍艦", "entity.immersive_aircraft.biplane": "雙翼飛機", "entity.immersive_aircraft.gyrodyne": "陀螺儀", "entity.immersive_aircraft.quadrocopter": "四旋翼機", "item.immersive_aircraft.hull": "船身", "item.immersive_aircraft.engine": "引擎", "item.immersive_aircraft.sail": "風帆", "item.immersive_aircraft.propeller": "螺旋槳", "item.immersive_aircraft.boiler": "鍋爐", "item.immersive_aircraft.enhanced_propeller": "增強型螺旋槳", "item.immersive_aircraft.eco_engine": "環保引擎", "item.immersive_aircraft.nether_engine": "何處引擎", "item.immersive_aircraft.steel_boiler": "鋼製鍋爐", "item.immersive_aircraft.industrial_gears": "工業齒輪", "item.immersive_aircraft.sturdy_pipes": "堅固的管道", "item.immersive_aircraft.gyroscope": "陀螺儀", "item.immersive_aircraft.hull_reinforcement": "船體強化", "item.immersive_aircraft.improved_landing_gear": "改良式起落架", "item.immersive_aircraft.rotary_cannon": "旋轉加農炮", "item.immersive_aircraft.bomb_bay": "炸彈灣", "item.immersive_aircraft.telescope": "望遠鏡", "item.immersive_aircraft.heavy_crossbow": "重弩", "item.immersive_aircraft.rotary_cannon.description": "使用火藥運行的快速火炮。", "item.immersive_aircraft.bomb_bay.description": "丟下 TNT，不會破壞磚塊，但會造成嚴重傷害。", "item.immersive_aircraft.telescope.description": "體積較大的窺視鏡。", "item.immersive_aircraft.heavy_crossbow.description": "重型十字弓，具有強大的衝力，需要使用箭矢。", "item.immersive_aircraft.item.upgrade": "飛機升級", "item.immersive_aircraft.item.weapon": "飛機武器", "item.immersive_aircraft.airship": "飛艇", "item.immersive_aircraft.cargo_airship": "貨運飛船", "item.immersive_aircraft.warship": "軍艦", "item.immersive_aircraft.biplane": "雙翼飛機", "item.immersive_aircraft.gyrodyne": "陀螺儀", "item.immersive_aircraft.quadrocopter": "四旋翼機", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "速度慢、耗油量大，但卻能攜帶整個倉庫。", "item.immersive_aircraft.warship.description": "飛行要塞，速度緩慢但裝備齊全。", "item.immersive_aircraft.biplane.description": "快速且相當可靠。確保您的跑道夠長。", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%%力量，繼續推！", "immersive_aircraft.gyrodyne_target_reached": "達到最低旋翼速度，準備起飛！", "immersive_aircraft.invalid_dimension": "這架飛機在這個次元裡無法運作。", "immersive_aircraft.out_of_ammo": "沒有彈藥了", "immersive_aircraft.repair": "%s%%已修復！", "immersive_aircraft.tried_dismount": "再按一次跳出！", "immersive_aircraft.fuel.none": "沒有燃料", "immersive_aircraft.fuel.out": "你的燃料用完了", "immersive_aircraft.fuel.low": "您的燃料不足！", "immersive_aircraft.fat.none": "沒有食物", "immersive_aircraft.fat.out": "你太餓了，無法飛行！", "option.immersive_aircraft.general": "一般選項", "option.immersive_aircraft.separateCamera": "在飛機上使用獨立的攝影機。", "option.immersive_aircraft.useThirdPersonByDefault": "預設為飛機中的第三人稱攝影機。", "option.immersive_aircraft.enableTrails": "花式蒸氣小徑。", "option.immersive_aircraft.enableAnimatedSails": "波濤洶湧的風帆", "option.immersive_aircraft.renderDistance": "以區塊為單位的渲染距離。", "option.immersive_aircraft.fuelConsumption": "燃料燃燒率。", "option.immersive_aircraft.windClearWeather": "基地風效應。", "option.immersive_aircraft.windRainWeather": "降雨時的風。", "option.immersive_aircraft.windThunderWeather": "打雷時會有額外的風。", "option.immersive_aircraft.repairSpeed": "每點擊一次進行維修。", "option.immersive_aircraft.repairExhaustion": "玩家每次點擊的疲勞度。", "option.immersive_aircraft.collisionDamage": "碰撞損壞。", "option.immersive_aircraft.burnFuelInCreative": "在創意模式中燃燒燃料。", "option.immersive_aircraft.acceptVanillaFuel": "接受香草燃料。", "option.immersive_aircraft.useCustomKeybindSystem": "使用多重關鍵綁定，可能會破壞某些 MOD。", "option.immersive_aircraft.showHotbarEngineGauge": "在 hotbar 上渲染引擎儀表。", "option.immersive_aircraft.enableCrashExplosion": "啟動爆炸。", "option.immersive_aircraft.enableCrashBlockDestruction": "啟用破壞性爆炸。", "option.immersive_aircraft.enableCrashFire": "啟用火焰爆炸。", "option.immersive_aircraft.crashExplosionRadius": "碰撞爆炸的大小。", "option.immersive_aircraft.crashDamage": "撞擊時損壞玩家。", "option.immersive_aircraft.preventKillThroughCrash": "不會在崩潰時殺死玩家。", "option.immersive_aircraft.healthBarRow": "抵銷車輛的健康條。", "option.immersive_aircraft.damagePerHealthPoint": "值越高，飛機越耐用。", "option.immersive_aircraft.weaponsAreDestructive": "允許某些武器摧毀區塊。", "option.immersive_aircraft.dropInventory": "丟掉庫存，而不是保存在飛行器項目中。", "option.immersive_aircraft.dropUpgrades": "丟掉升級和客製化，而不是保存在飛機項目中。", "option.immersive_aircraft.regenerateHealthEveryNTicks": "每隔一格自動再生健康值，模擬原版行為。", "option.immersive_aircraft.requireShiftForRepair": "只有在保持換檔時才能進行維修，否則只需進入車輛即可。", "immersive_aircraft.tooltip.no_target": "需要在地板上組裝！", "immersive_aircraft.tooltip.no_space": "空間不足！", "immersive_aircraft.slot.booster": "推進火箭", "immersive_aircraft.slot.weapon": "武器插槽", "immersive_aircraft.slot.boiler": "燃料槽", "immersive_aircraft.slot.banner": "橫幅插槽", "immersive_aircraft.slot.dye": "染色槽", "immersive_aircraft.slot.upgrade": "升級插槽", "immersive_aircraft.upgrade.enginespeed": "%s%% 引擎功率", "immersive_aircraft.upgrade.friction": "%s%%空氣摩擦", "immersive_aircraft.upgrade.acceleration": "%s%% 起飛速度", "immersive_aircraft.upgrade.durability": "%s%%耐久性", "immersive_aircraft.upgrade.fuel": "%s燃料需求百分比", "immersive_aircraft.upgrade.wind": "%s%% 風效應", "immersive_aircraft.upgrade.stabilizer": "%s%%穩定", "immersive_aircraft.tooltip.inventory": "包含 %s 項目。"}