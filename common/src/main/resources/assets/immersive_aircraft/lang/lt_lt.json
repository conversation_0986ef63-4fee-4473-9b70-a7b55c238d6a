{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Įtraukiantis or<PERSON>vis", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Atgalinis", "key.immersive_aircraft.multi_control_up": "Į viršų", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Ištrauki<PERSON> valdiklis", "key.immersive_aircraft.multi_control_push": "<PERSON><PERSON><PERSON><PERSON> \"Push Controller", "key.immersive_aircraft.multi_use": "<PERSON><PERSON><PERSON> g<PERSON> / montuot<PERSON>", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Atgalinis", "key.immersive_aircraft.fallback_control_up": "Į viršų", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Ištrauki<PERSON> valdiklis", "key.immersive_aircraft.fallback_control_push": "<PERSON><PERSON><PERSON><PERSON> \"Push Controller", "key.immersive_aircraft.fallback_use": "<PERSON><PERSON><PERSON> g<PERSON> / montuot<PERSON>", "key.immersive_aircraft.dismount": "Išmontuokite", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "Krovininis dir<PERSON>", "entity.immersive_aircraft.warship": "<PERSON><PERSON>", "entity.immersive_aircraft.biplane": "Dvigu<PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull": "Korpusa<PERSON>", "item.immersive_aircraft.engine": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Patobulintas sraigtas", "item.immersive_aircraft.eco_engine": "\"Eco\" variklis", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON><PERSON><PERSON> varik<PERSON>", "item.immersive_aircraft.steel_boiler": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.industrial_gears": "Pramoniniai krumpliaračiai", "item.immersive_aircraft.sturdy_pipes": "Tvirti vamzdžiai", "item.immersive_aircraft.gyroscope": "Giroskopas", "item.immersive_aircraft.hull_reinforcement": "Ko<PERSON><PERSON>o sutvir<PERSON>", "item.immersive_aircraft.improved_landing_gear": "Patobulinta važiuoklė", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON> pat<PERSON>", "item.immersive_aircraft.bomb_bay": "Bombų įlanka", "item.immersive_aircraft.telescope": "Teleskopas", "item.immersive_aircraft.heavy_crossbow": "Sun<PERSON><PERSON> a<PERSON>", "item.immersive_aircraft.rotary_cannon.description": "Gre<PERSON>i <PERSON> patrank<PERSON>, veikianti su šaunamaisiais milteliais.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> blo<PERSON>, bet daro did<PERSON>.", "item.immersive_aircraft.telescope.description": "Didesnės api<PERSON>ties akinių versija.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON><PERSON> a<PERSON>, t<PERSON><PERSON><PERSON> smūgį, <PERSON><PERSON><PERSON><PERSON> str<PERSON>.", "item.immersive_aircraft.item.upgrade": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "Krovininis dir<PERSON>", "item.immersive_aircraft.warship": "<PERSON><PERSON>", "item.immersive_aircraft.biplane": "Dvigu<PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Dirižabliai gal ir nėra greičiausios transporto priemonės, bet jais tikrai lengva manevruoti.", "item.immersive_aircraft.cargo_airship.description": "Lėtas ir alkanas de<PERSON>, bet veža visą sandėlį.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, bet gerai g<PERSON>.", "item.immersive_aircraft.biplane.description": "Greitas ir gana patiki<PERSON>. Įsitikinkite, kad jūs<PERSON> kilimo ir tūpimo takas yra pakankamai ilgas.", "item.immersive_aircraft.gyrodyne.description": "<PERSON><PERSON> varik<PERSON>, jei orlaivį galima varomi vien tik jėga? Gerai jį pastumkite ir jis skris!", "item.immersive_aircraft.quadrocopter.description": "Inžinerijos šedevras! 4 rotoriai, pritvirtinti prie bambuko. Puikiai tinka statybai, ir tiek.", "immersive_aircraft.gyrodyne_target": "%d%% Galia, stumkite toliau!", "immersive_aircraft.gyrodyne_target_reached": "Pasiektas minimalus rotor<PERSON>us greitis, pasirengta kilimu<PERSON>!", "immersive_aircraft.invalid_dimension": "Šis orlaivis neveikia šioje dimensijoje.", "immersive_aircraft.out_of_ammo": "Baigėsi šoviniai!", "immersive_aircraft.repair": "%s%% suremontuota!", "immersive_aircraft.tried_dismount": "Paspauskite dar kartą, kad i<PERSON>!", "immersive_aircraft.fuel.none": "Nėra degalų!", "immersive_aircraft.fuel.out": "Pritrūko de<PERSON>!", "immersive_aircraft.fuel.low": "Jums trūksta degalų!", "immersive_aircraft.fat.none": "Jo<PERSON>o maisto!", "immersive_aircraft.fat.out": "Esate per daug alkanas, kad sk<PERSON>!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON>", "option.immersive_aircraft.separateCamera": "Naudokite atskirą or<PERSON>.", "option.immersive_aircraft.useThirdPersonByDefault": "Numatytasis nustatymas - trečiojo asmens ka<PERSON>a <PERSON>.", "option.immersive_aircraft.enableTrails": "Išgalvoti garo takai.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON> bang<PERSON> bur<PERSON>.", "option.immersive_aircraft.renderDistance": "Atvaizdavimo atstumas blokai<PERSON>.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON> sude<PERSON> greitis.", "option.immersive_aircraft.windClearWeather": "Bazinio vėjo poveikis.", "option.immersive_aircraft.windRainWeather": "Vėjas kritulių metu.", "option.immersive_aircraft.windThunderWeather": "Papildomas vėjas griaustinio metu.", "option.immersive_aircraft.repairSpeed": "Remontas už paspaudimą.", "option.immersive_aircraft.repairExhaustion": "Ž<PERSON><PERSON><PERSON> išsekimas per paspaudimą.", "option.immersive_aircraft.collisionDamage": "Susidūrimo žala.", "option.immersive_aircraft.burnFuelInCreative": "<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON> de<PERSON> degal<PERSON>.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON>im<PERSON><PERSON> vanilinius degalus.", "option.immersive_aircraft.useCustomKeybindSystem": "Naudokite daug<PERSON><PERSON>ius keybindings, gali sugadinti tam tikrus modus.", "option.immersive_aircraft.showHotbarEngineGauge": "Atvaizduokite variklio matuoklį virš ka<PERSON> juosto<PERSON>.", "option.immersive_aircraft.enableCrashExplosion": "Įgalinti sprogimą.", "option.immersive_aircraft.enableCrashBlockDestruction": "Įgalinkite griaunamąjį sprogimą.", "option.immersive_aircraft.enableCrashFire": "Įgalinti ugninį sprogimą.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON>jos sprogimo d<PERSON>.", "option.immersive_aircraft.crashDamage": "Sugadinkite <PERSON>ą, kai jis sutri<PERSON>.", "option.immersive_aircraft.preventKillThroughCrash": "Žaidėjas nebus nužudytas, jei įvyks avarija.", "option.immersive_aircraft.healthBarRow": "Kompensuoti transporto priemonių sveikatos juostą.", "option.immersive_aircraft.damagePerHealthPoint": "Dėl didesnių verčių orlaiviai yra patvaresni.", "option.immersive_aircraft.weaponsAreDestructive": "Leisk<PERSON> kai kuriais gink<PERSON>s na<PERSON> b<PERSON>.", "option.immersive_aircraft.dropInventory": "Išmeskite inventorių, užuot išsaugoję jį or<PERSON>vio elemente.", "option.immersive_aircraft.dropUpgrades": "Išmeskite patobulinimus ir pritaikymą, už<PERSON>t išsaugoję juos or<PERSON>vio elemente.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatiškai atstato sveikatą kas tam tikrą laiką, imituodamas \"vanilės\" elgesį.", "option.immersive_aircraft.requireShiftForRepair": "Remontuokite tik laikydami pavarų perjungimo svirtį, kitu atveju tiesiog įvažiuokite į transporto priemonę.", "immersive_aircraft.tooltip.no_target": "Reikia surinkti ant grindų!", "immersive_aircraft.tooltip.no_space": "Nepakanka vietos!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON><PERSON> raketas", "immersive_aircraft.slot.weapon": "<PERSON><PERSON><PERSON>", "immersive_aircraft.slot.boiler": "<PERSON><PERSON>", "immersive_aircraft.slot.banner": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.dye": "Dažų lizdas", "immersive_aircraft.slot.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.upgrade.enginespeed": "%s%% variklio galia", "immersive_aircraft.upgrade.friction": "%s% % oro trintis", "immersive_aircraft.upgrade.acceleration": "%s%% kilimo greitis", "immersive_aircraft.upgrade.durability": "%s%% ilgaamžiškumas", "immersive_aircraft.upgrade.fuel": "%s%% degalų poreikis", "immersive_aircraft.upgrade.wind": "%s%% vėjo poveikis", "immersive_aircraft.upgrade.stabilizer": "%s% % stabilizavimas", "immersive_aircraft.tooltip.inventory": "Sudėtyje yra %s elementų."}