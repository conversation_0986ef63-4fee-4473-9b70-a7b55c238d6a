{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Flugzeuge", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Waffe/Mount verwenden", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Waffe/Mount verwenden", "key.immersive_aircraft.dismount": "Demontieren", "key.immersive_aircraft.boost": "Raketenschub", "entity.immersive_aircraft.airship": "Luftschiff", "entity.immersive_aircraft.cargo_airship": "Frachtluftschiff", "entity.immersive_aircraft.warship": "Kriegsschiff", "entity.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Kessel", "item.immersive_aircraft.enhanced_propeller": "Verbesserter Propeller", "item.immersive_aircraft.eco_engine": "Öko-Motor", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Stahlkessel", "item.immersive_aircraft.industrial_gears": "Industriegetriebe", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Rumpf-Verstärkung", "item.immersive_aircraft.improved_landing_gear": "Verbessertes Fahrwerk", "item.immersive_aircraft.rotary_cannon": "Drehrohrkanone", "item.immersive_aircraft.bomb_bay": "Bombenbucht", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Schwere Armbrust", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON>uer<PERSON><PERSON>, die mit Schießpulver betrieben wird.", "item.immersive_aircraft.bomb_bay.description": "Wirft TNT ab, zerstört keine Blöcke, verursacht aber großen Schaden.", "item.immersive_aircraft.telescope.description": "Eine größere Version des Fernglases.", "item.immersive_aircraft.heavy_crossbow.description": "Eine schwere Armbrust mit einem starken Durchschlag, für den Pfeile benötigt werden.", "item.immersive_aircraft.item.upgrade": "Upgrade des Flugzeugs", "item.immersive_aircraft.item.weapon": "Flugzeugwaffe", "item.immersive_aircraft.airship": "Luftschiff", "item.immersive_aircraft.cargo_airship": "Frachtluftschiff", "item.immersive_aircraft.warship": "Kriegsschiff", "item.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Luftschiffe sind vielleicht nicht die schnellsten Fahrzeuge, aber sie sind leicht zu manövrieren.", "item.immersive_aircraft.cargo_airship.description": "Langsam und treibstoffhungrig, aber er transportiert ein ganzes <PERSON>ger.", "item.immersive_aircraft.warship.description": "Eine fliegende Festung, lang<PERSON><PERSON>, aber schwer bewaffnet.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON><PERSON> und ziemlich zuverlässig. Achte da<PERSON>, dass deine Landebahn lang genug ist.", "item.immersive_aircraft.gyrodyne.description": "Wer braucht schon einen Motor, wenn man das Flugzeug mit reiner Muskelkraft antreiben kann? Gib ihm einen kräftigen Schubs und schon fliegt es los!", "item.immersive_aircraft.quadrocopter.description": "Ein Meisterwerk der Technik! 4 Rotoren, die auf ein Stück Bambus geschnallt sind. Perfekt zum Bauen, und das war's auch schon.", "immersive_aircraft.gyrodyne_target": "%d%% Power, mach weiter!", "immersive_aircraft.gyrodyne_target_reached": "Minimale Rotordrehzahl erreicht, bereit zum Abheben!", "immersive_aircraft.invalid_dimension": "Dieses Flugzeug funktioniert nicht in dieser Dimension.", "immersive_aircraft.out_of_ammo": "<PERSON><PERSON> Mu<PERSON> mehr!", "immersive_aircraft.repair": "%s%% repariert!", "immersive_aircraft.tried_dismount": "<PERSON><PERSON><PERSON> erneut, um herauszu<PERSON>en!", "immersive_aircraft.fuel.none": "<PERSON><PERSON>!", "immersive_aircraft.fuel.out": "Dir ist der Sprit ausgegangen!", "immersive_aircraft.fuel.low": "Du hast nur noch wenig Treibstoff!", "immersive_aircraft.fat.none": "<PERSON><PERSON>!", "immersive_aircraft.fat.out": "Du bist zu hungrig zum Fliegen!", "option.immersive_aircraft.general": "Allgemeine Optionen", "option.immersive_aircraft.separateCamera": "Verwende eine separate Kamera im Flugzeug.", "option.immersive_aircraft.useThirdPersonByDefault": "Standardmäßig ist die Kamera im Flugzeug in der dritten Person.", "option.immersive_aircraft.enableTrails": "Schicke Dampfpfade.", "option.immersive_aircraft.enableAnimatedSails": "Wavey wavey Sails.", "option.immersive_aircraft.renderDistance": "Renderabstand in Blöcken.", "option.immersive_aircraft.fuelConsumption": "Kraftstoffverbrennungsrate.", "option.immersive_aircraft.windClearWeather": "Basis-Wind-Effekt.", "option.immersive_aircraft.windRainWeather": "Wind bei Niederschlag.", "option.immersive_aircraft.windThunderWeather": "Extra Wind beim <PERSON>r.", "option.immersive_aircraft.repairSpeed": "Reparatur pro Klick.", "option.immersive_aircraft.repairExhaustion": "Spielererschöpfunk pro Klick.", "option.immersive_aircraft.collisionDamage": "Kollisionsschaden.", "option.immersive_aircraft.burnFuelInCreative": "Verbrenne Treibstoff im Kreativmodus.", "option.immersive_aircraft.acceptVanillaFuel": "Akzeptiere Vanillekraftstoff.", "option.immersive_aircraft.useCustomKeybindSystem": "Verwende Multi-Keybindings, das kann bestimmte Mods kaputt machen.", "option.immersive_aircraft.showHotbarEngineGauge": "Rendere die Motoranzeige über die Hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Aktiviere die Explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktiviere die zerstörerische Explosion.", "option.immersive_aircraft.enableCrashFire": "Aktiviere die feurige Explosion.", "option.immersive_aircraft.crashExplosionRadius": "Größe der Crash-Explosion.", "option.immersive_aircraft.crashDamage": "Beschädige den Spieler bei einem Absturz.", "option.immersive_aircraft.preventKillThroughCrash": "Der Spieler wird bei einem Absturz nicht getötet.", "option.immersive_aircraft.healthBarRow": "Versetze die Gesundheitsleiste von Fahrzeugen.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON>here Werte machen Flugzeuge langlebiger.", "option.immersive_aircraft.weaponsAreDestructive": "Erlaube einigen W<PERSON>fen, <PERSON><PERSON><PERSON><PERSON> zu zerstören.", "option.immersive_aircraft.dropInventory": "Lass das Inventar fallen, anstatt es im Flugzeugobjekt zu speichern.", "option.immersive_aircraft.dropUpgrades": "Lass die Upgrades und Anpassungen weg, anstatt sie im Flugzeugobjekt zu speichern.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneriert automatisch Gesundheit jedes g<PERSON>, simulier<PERSON> vanilla Verhalten.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON>, wenn Shift gedr<PERSON>t halt, sonst einfach Fahrzeug betreten.", "immersive_aircraft.tooltip.no_target": "Muss auf dem Boden montiert werden!", "immersive_aircraft.tooltip.no_space": "Nicht genug Platz!", "immersive_aircraft.slot.booster": "Boost-Raketen", "immersive_aircraft.slot.weapon": "Waffenplatz", "immersive_aircraft.slot.boiler": "Kraftstoffschlitz", "immersive_aircraft.slot.banner": "<PERSON>", "immersive_aircraft.slot.dye": "Färb<PERSON>chlitz", "immersive_aircraft.slot.upgrade": "Upgrade-Slot", "immersive_aircraft.upgrade.enginespeed": "%s%% Motorleistung", "immersive_aircraft.upgrade.friction": "%s%% Luftreibung", "immersive_aircraft.upgrade.acceleration": "%s%% Abfluggeschwindigkeit", "immersive_aircraft.upgrade.durability": "%s%% Haltbarkeit", "immersive_aircraft.upgrade.fuel": "%s%% Kraftstoffbedarf", "immersive_aircraft.upgrade.wind": "%s%% Windeffekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilisierend", "immersive_aircraft.tooltip.inventory": "Enthält %s Artikel."}