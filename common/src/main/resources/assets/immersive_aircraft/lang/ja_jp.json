{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "左", "key.immersive_aircraft.multi_control_right": "右", "key.immersive_aircraft.multi_control_forward": "前進", "key.immersive_aircraft.multi_control_backward": "後退", "key.immersive_aircraft.multi_control_up": "上", "key.immersive_aircraft.multi_control_down": "下", "key.immersive_aircraft.multi_control_pull": "コントローラーを引く", "key.immersive_aircraft.multi_control_push": "コントローラーを押す", "key.immersive_aircraft.multi_use": "武器/マウントの使用", "key.immersive_aircraft.fallback_control_left": "左", "key.immersive_aircraft.fallback_control_right": "右", "key.immersive_aircraft.fallback_control_forward": "前進", "key.immersive_aircraft.fallback_control_backward": "後退", "key.immersive_aircraft.fallback_control_up": "上", "key.immersive_aircraft.fallback_control_down": "下", "key.immersive_aircraft.fallback_control_pull": "コントローラーを引く", "key.immersive_aircraft.fallback_control_push": "コントローラーを押す", "key.immersive_aircraft.fallback_use": "武器/マウントの使用", "key.immersive_aircraft.dismount": "降りる", "key.immersive_aircraft.boost": "ロケットブースト", "entity.immersive_aircraft.airship": "飛行船", "entity.immersive_aircraft.cargo_airship": "貨物飛行船", "entity.immersive_aircraft.warship": "飛行艦", "entity.immersive_aircraft.biplane": "複葉機", "entity.immersive_aircraft.gyrodyne": "ジャイロダイン", "entity.immersive_aircraft.quadrocopter": "クワッドローター", "item.immersive_aircraft.hull": "船体", "item.immersive_aircraft.engine": "エンジン", "item.immersive_aircraft.sail": "帆", "item.immersive_aircraft.propeller": "プロペラ", "item.immersive_aircraft.boiler": "ボイラー", "item.immersive_aircraft.enhanced_propeller": "強化プロペラ", "item.immersive_aircraft.eco_engine": "エコエンジン", "item.immersive_aircraft.nether_engine": "ネザーエンジン", "item.immersive_aircraft.steel_boiler": "鋼鉄製ボイラー", "item.immersive_aircraft.industrial_gears": "産業用歯車", "item.immersive_aircraft.sturdy_pipes": "頑丈なパイプ", "item.immersive_aircraft.gyroscope": "ジャイロスコープ", "item.immersive_aircraft.hull_reinforcement": "船体補強", "item.immersive_aircraft.improved_landing_gear": "改良型の降着装置", "item.immersive_aircraft.rotary_cannon": "ロータリー・キャノン", "item.immersive_aircraft.bomb_bay": "爆弾倉", "item.immersive_aircraft.telescope": "望遠鏡", "item.immersive_aircraft.heavy_crossbow": "ヘビークロスボウ", "item.immersive_aircraft.rotary_cannon.description": "火薬を使った速射砲。", "item.immersive_aircraft.bomb_bay.description": "TNTを落とし、ブロックは破壊しないが、大ダメージを与える。", "item.immersive_aircraft.telescope.description": "望遠鏡の大型版。", "item.immersive_aircraft.heavy_crossbow.description": "強い威力を持つ重いクロスボウで、矢が必要。", "item.immersive_aircraft.item.upgrade": "航空機のアップグレード", "item.immersive_aircraft.item.weapon": "航空機兵器", "item.immersive_aircraft.airship": "飛行船", "item.immersive_aircraft.cargo_airship": "貨物飛行船", "item.immersive_aircraft.warship": "飛行艦", "item.immersive_aircraft.biplane": "複葉機", "item.immersive_aircraft.gyrodyne": "ジャイロダイン", "item.immersive_aircraft.quadrocopter": "クワッドローター", "item.immersive_aircraft.airship.description": "飛行船は最速の航空機じゃないが、操縦は簡単だ。", "item.immersive_aircraft.cargo_airship.description": "遅くて燃費が悪いが、倉庫を丸ごと運べる。", "item.immersive_aircraft.warship.description": "低速だが重武装の空飛ぶ要塞。", "item.immersive_aircraft.biplane.description": "速くて信頼できる。滑走路が十分な長さがあることを確認してください。", "item.immersive_aircraft.gyrodyne.description": "純粋な腕力で航空機を動かすことができるのなら、機関は必要ないだろう？ 脚を働かせて、大空に舞い上がろう！", "item.immersive_aircraft.quadrocopter.description": "竹の骨組みに4つのプロペラを括り付けた、工学の傑作だ。組み立てには最適だ。", "immersive_aircraft.gyrodyne_target": "回転数は%d%%、漕ぎ続けてください！", "immersive_aircraft.gyrodyne_target_reached": "最低ローター速度に到達、離陸準備完了！", "immersive_aircraft.invalid_dimension": "この機体はこのディメンションでは機能しない。", "immersive_aircraft.out_of_ammo": "弾切れだ！", "immersive_aircraft.repair": "%s%%修理しました！", "immersive_aircraft.tried_dismount": "もう一度押すと飛び降ります！", "immersive_aircraft.fuel.none": "燃料がありません！", "immersive_aircraft.fuel.out": "燃料が尽きました！", "immersive_aircraft.fuel.low": "燃料が残り少ないです！", "immersive_aircraft.fat.none": "満腹度ゲージは空っぽだ", "immersive_aircraft.fat.out": "飛ぶにはお腹が空きすぎる", "option.immersive_aircraft.general": "一般の設定", "option.immersive_aircraft.separateCamera": "航空機を使うときは視点を変える", "option.immersive_aircraft.useThirdPersonByDefault": "航空機を使用する際は、三人称視点に変える", "option.immersive_aircraft.enableTrails": "蒸気の跡", "option.immersive_aircraft.enableAnimatedSails": "帆が揺れる", "option.immersive_aircraft.renderDistance": "ブロック単位での描画距離", "option.immersive_aircraft.fuelConsumption": "燃料の燃焼率", "option.immersive_aircraft.windClearWeather": "基本の風による影響", "option.immersive_aircraft.windRainWeather": "雨天時の風による影響", "option.immersive_aircraft.windThunderWeather": "雷雨時の風による影響", "option.immersive_aircraft.repairSpeed": "クリックごとの修理。", "option.immersive_aircraft.repairExhaustion": "クリックごとにプレイヤーが消耗する。", "option.immersive_aircraft.collisionDamage": "衝突によるダメージ", "option.immersive_aircraft.burnFuelInCreative": "クリエイティブモードでも燃料を消費する", "option.immersive_aircraft.acceptVanillaFuel": "バニラ燃料を許可する", "option.immersive_aircraft.useCustomKeybindSystem": "複数のキー割り当てを使用する（一部のmodが壊れる可能性がある）", "option.immersive_aircraft.showHotbarEngineGauge": "エンジンゲージをホットバーの上に表示する。", "option.immersive_aircraft.enableCrashExplosion": "爆発を有効にする。", "option.immersive_aircraft.enableCrashBlockDestruction": "破壊的な爆発を可能にする。", "option.immersive_aircraft.enableCrashFire": "燃えるような爆発を可能にする。", "option.immersive_aircraft.crashExplosionRadius": "衝突爆発の規模。", "option.immersive_aircraft.crashDamage": "衝突したプレイヤーにダメージを与える。", "option.immersive_aircraft.preventKillThroughCrash": "衝突してもプレイヤーは死なない。", "option.immersive_aircraft.healthBarRow": "車両のヘルスバーを相殺する。", "option.immersive_aircraft.damagePerHealthPoint": "数値が高いほど、航空機の耐久性が高くなる。", "option.immersive_aircraft.weaponsAreDestructive": "いくつかの武器でブロックを破壊できるようにする。", "option.immersive_aircraft.dropInventory": "機体アイテム内に保存する代わりに、インベントリをドロップする。", "option.immersive_aircraft.dropUpgrades": "アップグレードやカスタマイズを、機体アイテム内に保存するのではなく、ドロップする。", "option.immersive_aircraft.regenerateHealthEveryNTicks": "バニラの挙動をシミュレートし、指定されたティックごとに自動的にヘルスを回復する。", "option.immersive_aircraft.requireShiftForRepair": "修理はシフトホールド時のみで、それ以外は車両に乗り込むだけ。", "immersive_aircraft.tooltip.no_target": "床で組み立てる必要がある！", "immersive_aircraft.tooltip.no_space": "スペースが足りない！", "immersive_aircraft.slot.booster": "ブースター", "immersive_aircraft.slot.weapon": "武器スロット", "immersive_aircraft.slot.boiler": "燃料スロット", "immersive_aircraft.slot.banner": "旗スロット", "immersive_aircraft.slot.dye": "染料スロット", "immersive_aircraft.slot.upgrade": "アップグレードスロット", "immersive_aircraft.upgrade.enginespeed": "%s%%エンジン出力", "immersive_aircraft.upgrade.friction": "%s%%空気抵抗", "immersive_aircraft.upgrade.acceleration": "%s%%離陸速度", "immersive_aircraft.upgrade.durability": "%s%%耐久値", "immersive_aircraft.upgrade.fuel": "%s%%必要な燃料", "immersive_aircraft.upgrade.wind": "%s%%風影響", "immersive_aircraft.upgrade.stabilizer": "%s%%安定化", "immersive_aircraft.tooltip.inventory": "アイテムを%s スタック保有"}