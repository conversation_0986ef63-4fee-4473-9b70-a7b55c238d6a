{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Bakåt", "key.immersive_aircraft.multi_control_up": "Upp", "key.immersive_aircraft.multi_control_down": "<PERSON>", "key.immersive_aircraft.multi_control_pull": "<PERSON>a sty<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_push": "<PERSON><PERSON> styrenhet", "key.immersive_aircraft.multi_use": "Använd vapen/montering", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Bakåt", "key.immersive_aircraft.fallback_control_up": "Upp", "key.immersive_aircraft.fallback_control_down": "<PERSON>", "key.immersive_aircraft.fallback_control_pull": "<PERSON>a sty<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_push": "<PERSON><PERSON> styrenhet", "key.immersive_aircraft.fallback_use": "Använd vapen/montering", "key.immersive_aircraft.dismount": "Kliva av", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "Luftskepp", "entity.immersive_aircraft.cargo_airship": "Luftskepp för last", "entity.immersive_aircraft.warship": "Krigsfartyg", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "Trampkopter", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull": "Flygkropp", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "<PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Förbättrad propeller", "item.immersive_aircraft.eco_engine": "Ekomotor", "item.immersive_aircraft.nether_engine": "Nethermotor", "item.immersive_aircraft.steel_boiler": "Stålpanna", "item.immersive_aircraft.industrial_gears": "Industriel<PERSON>", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Flygkroppsförstärkning", "item.immersive_aircraft.improved_landing_gear": "Förbättrade landställ", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON> kanon", "item.immersive_aircraft.bomb_bay": "Bombhöjd", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Tungt armborst", "item.immersive_aircraft.rotary_cannon.description": "Snabbskjutande kanon som drivs med krut.", "item.immersive_aircraft.bomb_bay.description": "Droppar TNT, förstör inte block men orsakar stor skada.", "item.immersive_aircraft.telescope.description": "En tjockare version av kikaren.", "item.immersive_aircraft.heavy_crossbow.description": "Ett tungt armborst med kraftfull slagkraft, kräver pilar.", "item.immersive_aircraft.item.upgrade": "Luftfarkostsuppgradering", "item.immersive_aircraft.item.weapon": "Flygplan Vapen", "item.immersive_aircraft.airship": "Luftskepp", "item.immersive_aircraft.cargo_airship": "Luftskepp för last", "item.immersive_aircraft.warship": "Krigsfartyg", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "Trampkopter", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Luftskepp är kanske inte de snabbaste luftfarkosterna, men de är lättmanövrerade!", "item.immersive_aircraft.cargo_airship.description": "Långsam och bränsletörstig men rymmer ett helt lager.", "item.immersive_aircraft.warship.description": "En flygande fästning, långsam men tungt beväpnad.", "item.immersive_aircraft.biplane.description": "Snabb och ganska tillförlitlig. Se till att din landningsbana är tillräckligt lång.", "item.immersive_aircraft.gyrodyne.description": "Vem behöver en motor när man kan driva en luftfarkost med ren muskelkraft? Trampa på som en gnu och flyg upp, högt i det blå!", "item.immersive_aircraft.quadrocopter.description": "Fyra propellrar fastspända på bambu – ett mästerverk av ingenjörskonst! Perfekt för att bygga med och det är ungefär allt.", "immersive_aircraft.gyrodyne_target": "%d%% kraft – fortsätt trampa!", "immersive_aircraft.gyrodyne_target_reached": "<PERSON><PERSON> för start!", "immersive_aircraft.invalid_dimension": "Detta flygplan fungerar inte i denna dimension.", "immersive_aircraft.out_of_ammo": "Slut på ammunition!", "immersive_aircraft.repair": "%s%% reparerad!", "immersive_aircraft.tried_dismount": "Tryck igen för att hoppa ut!", "immersive_aircraft.fuel.none": "Det finns inget bränsle!", "immersive_aircraft.fuel.out": "<PERSON><PERSON><PERSON><PERSON><PERSON> har tagit slut!", "immersive_aircraft.fuel.low": "Det är ont om bränsle!", "immersive_aircraft.fat.none": "Du orkar inte trampa längre!", "immersive_aircraft.fat.out": "Du är helt utmattad!", "option.immersive_aircraft.general": "Allmänna inställningar", "option.immersive_aircraft.separateCamera": "Använd en annan kamera i luftfarkoster", "option.immersive_aircraft.useThirdPersonByDefault": "Använd tredjepersonsperspektiv i luftfarkoster", "option.immersive_aircraft.enableTrails": "Ångspår", "option.immersive_aircraft.enableAnimatedSails": "Fladdrande segel", "option.immersive_aircraft.renderDistance": "Synligt avstånd i block", "option.immersive_aircraft.fuelConsumption": "Bränsleförbränningshastighet", "option.immersive_aircraft.windClearWeather": "Vindpåverkan vid klart väder", "option.immersive_aircraft.windRainWeather": "Vindpåverkan vid nederbörd", "option.immersive_aircraft.windThunderWeather": "Vindpåverkan vid åskväder", "option.immersive_aircraft.repairSpeed": "Reparation per klick.", "option.immersive_aircraft.repairExhaustion": "Utmattning av spelare per klick.", "option.immersive_aircraft.collisionDamage": "Kollisionsskada", "option.immersive_aircraft.burnFuelInCreative": "Förbränn bränsle i kreativt läge", "option.immersive_aircraft.acceptVanillaFuel": "Tillåt vanliga bränslen (kol, trä, o.s.v.)", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON><PERSON><PERSON><PERSON> flera kortkommandon (kan få vissa mod att sluta fungera)", "option.immersive_aircraft.showHotbarEngineGauge": "Placera motormätaren över hotbaren.", "option.immersive_aircraft.enableCrashExplosion": "Möjliggör explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Möjliggör destruktiv explosion.", "option.immersive_aircraft.enableCrashFire": "Möjliggör en brinnande explosion.", "option.immersive_aircraft.crashExplosionRadius": "Storlek på krockexplosion.", "option.immersive_aircraft.crashDamage": "Skada spelaren vid en krasch.", "option.immersive_aircraft.preventKillThroughCrash": "Kommer inte att döda spelaren vid en krasch.", "option.immersive_aircraft.healthBarRow": "Kompensera för fordonens hälsopåverkan.", "option.immersive_aircraft.damagePerHealthPoint": "Högre värden gör flygplanet mer hållbart.", "option.immersive_aircraft.weaponsAreDestructive": "Tillåt vissa vapen att förstöra block.", "option.immersive_aircraft.dropInventory": "Släpp inventeringen istället för att spara den i flygplansobjektet.", "option.immersive_aircraft.dropUpgrades": "Släpp uppgraderingarna och anpassningen, istä<PERSON>t för att spara den i flygplansobjektet.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Återställer automatiskt hälsan varje given tick, vilket simulerar vaniljbeteende.", "option.immersive_aircraft.requireShiftForRepair": "Reparera endast när du håller växeln, annars är det bara att köra in i fordonet.", "immersive_aircraft.tooltip.no_target": "Måste monteras på golvet!", "immersive_aircraft.tooltip.no_space": "Inte tillräckligt med utrymme!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.weapon": "Vapenplat<PERSON>", "immersive_aircraft.slot.boiler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.banner": "<PERSON><PERSON>", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Uppgradering", "immersive_aircraft.upgrade.enginespeed": "%s%% motoreffekt", "immersive_aircraft.upgrade.friction": "%s%% luftmotstånd", "immersive_aircraft.upgrade.acceleration": "%s%% starthastighet", "immersive_aircraft.upgrade.durability": "%s%% hållbarhet", "immersive_aircraft.upgrade.fuel": "%s%% bränslebehov", "immersive_aircraft.upgrade.wind": "%s%% vindpåverkan", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilisering", "immersive_aircraft.tooltip.inventory": "Innehåller %s artiklar."}