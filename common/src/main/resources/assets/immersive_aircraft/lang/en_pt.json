{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Airship", "key.immersive_aircraft.multi_control_left": "Left", "key.immersive_aircraft.multi_control_right": "Right", "key.immersive_aircraft.multi_control_forward": "Forward", "key.immersive_aircraft.multi_control_backward": "Backward", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Use weapon/mount, yarrr!", "key.immersive_aircraft.fallback_control_left": "Left", "key.immersive_aircraft.fallback_control_right": "Right", "key.immersive_aircraft.fallback_control_forward": "Forward", "key.immersive_aircraft.fallback_control_backward": "Backward", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Use weapon/mount, yarrr!", "key.immersive_aircraft.dismount": "Dismount", "key.immersive_aircraft.boost": "Rocket boost", "entity.immersive_aircraft.airship": "Airship", "entity.immersive_aircraft.cargo_airship": "Cargo Airship", "entity.immersive_aircraft.warship": "Warship", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Hull", "item.immersive_aircraft.engine": "Engine", "item.immersive_aircraft.sail": "Sail", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Enhan<PERSON>", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Steel Boiler", "item.immersive_aircraft.industrial_gears": "Industrial Gears", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroscope", "item.immersive_aircraft.hull_reinforcement": "Hull Reinforcement", "item.immersive_aircraft.improved_landing_gear": "Improved Landin' Gear", "item.immersive_aircraft.rotary_cannon": "Rotary Cannon, yarrr!", "item.immersive_aircraft.bomb_bay": "Bomb Bay, har har!", "item.immersive_aircraft.telescope": "Telescope, matey!", "item.immersive_aircraft.heavy_crossbow": "Heavy Crossbow, aye!", "item.immersive_aircraft.rotary_cannon.description": "Fast-firin' cannon runnin' with gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Drops TNT, don’t destroy blocks but deals heavy damage, yarrr!", "item.immersive_aircraft.telescope.description": "A bulkier version o' the spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "A heavy crossbow with a powerful punch, be requirin' arrows.", "item.immersive_aircraft.item.upgrade": "Aircraft Upgrade", "item.immersive_aircraft.item.weapon": "Aircraft Weapon, arrr!", "item.immersive_aircraft.airship": "Airship", "item.immersive_aircraft.cargo_airship": "Cargo Airship", "item.immersive_aircraft.warship": "Warship", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Slow and fuel hungry but carries an entire storage.", "item.immersive_aircraft.warship.description": "A flyin' fortress, slow but heavily armed.", "item.immersive_aircraft.biplane.description": "This trusty, rustic biplane will take ye anywhere fast and rather reliably. Make sure yer runway be long enough.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Power, keep pushin'!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotor speed reached, ready fer takeoff!", "immersive_aircraft.invalid_dimension": "This craft don’t work in this here dimension.", "immersive_aircraft.out_of_ammo": "Out of ammo, matey!", "immersive_aircraft.repair": "%s%% repaired, arr!", "immersive_aircraft.tried_dismount": "Press again to jump out, yarrr!", "immersive_aircraft.fuel.none": "No fuel!", "immersive_aircraft.fuel.out": "Ye ran out o' fuel!", "immersive_aircraft.fuel.low": "Ye be low on fuel!", "immersive_aircraft.fat.none": "No food!", "immersive_aircraft.fat.out": "Ye be too hungry t' fly!", "option.immersive_aircraft.general": "General Options", "option.immersive_aircraft.separateCamera": "Use a separate camera in aircraft.", "option.immersive_aircraft.useThirdPersonByDefault": "Defaults t' third person camera in aircraft.", "option.immersive_aircraft.enableTrails": "Fancy steam trails.", "option.immersive_aircraft.enableAnimatedSails": "Wavey wavey Sails.", "option.immersive_aircraft.renderDistance": "Render distance in blocks.", "option.immersive_aircraft.fuelConsumption": "Fuel burn rate.", "option.immersive_aircraft.windClearWeather": "Base wind effect.", "option.immersive_aircraft.windRainWeather": "Wind at rainfall.", "option.immersive_aircraft.windThunderWeather": "Extra wind at thunder.", "option.immersive_aircraft.repairSpeed": "Repair per click, matey!", "option.immersive_aircraft.repairExhaustion": "Player exhaustion per click, arr.", "option.immersive_aircraft.collisionDamage": "Collision damage.", "option.immersive_aircraft.burnFuelInCreative": "Burn fuel in creative mode.", "option.immersive_aircraft.acceptVanillaFuel": "Accept vanilla fuel.", "option.immersive_aircraft.useCustomKeybindSystem": "Use multi-keybindings, may break certain mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Render the engine gauge over the hotbar, aye!", "option.immersive_aircraft.enableCrashExplosion": "Enable explosion, aye!", "option.immersive_aircraft.enableCrashBlockDestruction": "Enable destructive explosion, matey!", "option.immersive_aircraft.enableCrashFire": "Enable fiery explosion, arr!", "option.immersive_aircraft.crashExplosionRadius": "Size of crash explosion, matey!", "option.immersive_aircraft.crashDamage": "Damage the scallywag on a crash.", "option.immersive_aircraft.preventKillThroughCrash": "Will not send the scallywag to <PERSON> on a crash.", "option.immersive_aircraft.healthBarRow": "Offset the health bar of vessels.", "option.immersive_aircraft.damagePerHealthPoint": "Higher values make the craft more durable, aye!", "option.immersive_aircraft.weaponsAreDestructive": "Allow some cannons t' destroy blocks.", "option.immersive_aircraft.dropInventory": "Drop yer booty, instead o' keepin' it within the aircraft item.", "option.immersive_aircraft.dropUpgrades": "Drop the upgrades an' customization, instead o' keepin' it within the aircraft item.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatically regenerates health e'ery tick, simulating vanilla behavior, aye.", "option.immersive_aircraft.requireShiftForRepair": "Only repair when holdin' shift, otherwise just enter the vessel.", "immersive_aircraft.tooltip.no_target": "Needs t' be assembled on the deck!", "immersive_aircraft.tooltip.no_space": "Not enough space, matey!", "immersive_aircraft.slot.booster": "Boost rockets", "immersive_aircraft.slot.weapon": "Weapon slot, matey!", "immersive_aircraft.slot.boiler": "Fuel slot", "immersive_aircraft.slot.banner": "Banner slot", "immersive_aircraft.slot.dye": "Dye slot", "immersive_aircraft.slot.upgrade": "Upgrade slot", "immersive_aircraft.upgrade.enginespeed": "%s%% engine power", "immersive_aircraft.upgrade.friction": "%s%% air friction", "immersive_aircraft.upgrade.acceleration": "%s%% takeoff speed", "immersive_aircraft.upgrade.durability": "%s%% durability", "immersive_aircraft.upgrade.fuel": "%s%% fuel requirement", "immersive_aircraft.upgrade.wind": "%s%% wind effect", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizin'", "immersive_aircraft.tooltip.inventory": "Contains %s items."}