{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Иммерсивный самолет", "key.immersive_aircraft.multi_control_left": "Слева", "key.immersive_aircraft.multi_control_right": "Правильно", "key.immersive_aircraft.multi_control_forward": "Вперед", "key.immersive_aircraft.multi_control_backward": "Назад", "key.immersive_aircraft.multi_control_up": "Ввер<PERSON>", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Контроллер тяги", "key.immersive_aircraft.multi_control_push": "Нажимной контроллер", "key.immersive_aircraft.multi_use": "Используй оружие/маунт", "key.immersive_aircraft.fallback_control_left": "Слева", "key.immersive_aircraft.fallback_control_right": "Правильно", "key.immersive_aircraft.fallback_control_forward": "Вперед", "key.immersive_aircraft.fallback_control_backward": "Назад", "key.immersive_aircraft.fallback_control_up": "Ввер<PERSON>", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Контроллер тяги", "key.immersive_aircraft.fallback_control_push": "Нажимной контроллер", "key.immersive_aircraft.fallback_use": "Используй оружие/маунт", "key.immersive_aircraft.dismount": "Демонтаж", "key.immersive_aircraft.boost": "Ракетный форсаж", "entity.immersive_aircraft.airship": "Ди<PERSON><PERSON>ж<PERSON>бль", "entity.immersive_aircraft.cargo_airship": "Грузовой дирижабль", "entity.immersive_aircraft.warship": "Военный корабль", "entity.immersive_aircraft.biplane": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Квадрокоптер", "item.immersive_aircraft.hull": "Корпус", "item.immersive_aircraft.engine": "Двигатель", "item.immersive_aircraft.sail": "<PERSON>а<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Пропеллер", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Усовершенствованный пропеллер", "item.immersive_aircraft.eco_engine": "Экодвигатель", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Стальной котел", "item.immersive_aircraft.industrial_gears": "Промышленные шестерни", "item.immersive_aircraft.sturdy_pipes": "Прочные трубы", "item.immersive_aircraft.gyroscope": "Гироскоп", "item.immersive_aircraft.hull_reinforcement": "Усиление корпуса", "item.immersive_aircraft.improved_landing_gear": "Улучшенная посадочная передача", "item.immersive_aircraft.rotary_cannon": "Ротационная пушка", "item.immersive_aircraft.bomb_bay": "Бомбовый отсек", "item.immersive_aircraft.telescope": "Телескоп", "item.immersive_aircraft.heavy_crossbow": "Тяжелый арбалет", "item.immersive_aircraft.rotary_cannon.description": "Быстрострельная пушка, работающая на порохе.", "item.immersive_aircraft.bomb_bay.description": "Сбрасывает TNT, не разрушает блоки, но наносит большой урон.", "item.immersive_aircraft.telescope.description": "Более громоздкая версия подзорной трубы.", "item.immersive_aircraft.heavy_crossbow.description": "Тяжелый арбалет с мощным ударом, требует стрел.", "item.immersive_aircraft.item.upgrade": "Модернизация самолета", "item.immersive_aircraft.item.weapon": "Авиационное оружие", "item.immersive_aircraft.airship": "Ди<PERSON><PERSON>ж<PERSON>бль", "item.immersive_aircraft.cargo_airship": "Грузовой дирижабль", "item.immersive_aircraft.warship": "Военный корабль", "item.immersive_aircraft.biplane": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Квадрокоптер", "item.immersive_aircraft.airship.description": "Может быть, дирижабли и не самое быстрое транспортное средство, но ими точно легко маневрировать.", "item.immersive_aircraft.cargo_airship.description": "Медленный и топливно голодный, но везет целый склад.", "item.immersive_aircraft.warship.description": "Летающая крепость, медленная, но хорошо вооруженная.", "item.immersive_aircraft.biplane.description": "Быстрый и довольно надежный. Убедись, что твоя взлетно-посадочная полоса достаточно длинная.", "item.immersive_aircraft.gyrodyne.description": "Кому нужен двигатель, если можно привести самолет в движение чистыми мускулами? Дайте ему хороший толчок, и он полетит!", "item.immersive_aircraft.quadrocopter.description": "Шедевр инженерной мысли! 4 ротора, привязанные к какому-то бамбуку. Идеально подходит для строительства, да и только.", "immersive_aircraft.gyrodyne_target": "%d%% Сила, продолжай толкать!", "immersive_aircraft.gyrodyne_target_reached": "Минимальная скорость вращения ротора достигнута, готов к взлету!", "immersive_aircraft.invalid_dimension": "Этот самолет не работает в этом измерении.", "immersive_aircraft.out_of_ammo": "Кончились патроны!", "immersive_aircraft.repair": "%s%% Отремонтировано!", "immersive_aircraft.tried_dismount": "Нажми еще раз, чтобы выпрыгнуть!", "immersive_aircraft.fuel.none": "Никакого топлива!", "immersive_aircraft.fuel.out": "У тебя закончилось топливо!", "immersive_aircraft.fuel.low": "У тебя мало топлива!", "immersive_aircraft.fat.none": "Никакой еды!", "immersive_aircraft.fat.out": "Ты слишком голоден, чтобы летать!", "option.immersive_aircraft.general": "Общие параметры", "option.immersive_aircraft.separateCamera": "Используй отдельную камеру в самолетах.", "option.immersive_aircraft.useThirdPersonByDefault": "По умолчанию в самолете установлена камера от третьего лица.", "option.immersive_aircraft.enableTrails": "Модные паровые дорожки.", "option.immersive_aircraft.enableAnimatedSails": "Волнообразные волнообразные паруса.", "option.immersive_aircraft.renderDistance": "Рендерируй расстояние в блоках.", "option.immersive_aircraft.fuelConsumption": "Скорость сгорания топлива.", "option.immersive_aircraft.windClearWeather": "Эффект базового ветра.", "option.immersive_aircraft.windRainWeather": "Ветер при осадках.", "option.immersive_aircraft.windThunderWeather": "Дополнительный ветер при грозе.", "option.immersive_aircraft.repairSpeed": "Ремонт за клик.", "option.immersive_aircraft.repairExhaustion": "Истощение игрока за клик.", "option.immersive_aircraft.collisionDamage": "Повреждения при столкновении.", "option.immersive_aircraft.burnFuelInCreative": "Сжигай топливо в творческом режиме.", "option.immersive_aircraft.acceptVanillaFuel": "Прими ванильное топливо.", "option.immersive_aircraft.useCustomKeybindSystem": "Используй многоключевые привязки, может сломать некоторые моды.", "option.immersive_aircraft.showHotbarEngineGauge": "Изобразите индикатор двигателя над хотбаром.", "option.immersive_aircraft.enableCrashExplosion": "Включи взрыв.", "option.immersive_aircraft.enableCrashBlockDestruction": "Включи разрушительный взрыв.", "option.immersive_aircraft.enableCrashFire": "Включи огненный взрыв.", "option.immersive_aircraft.crashExplosionRadius": "Размер взрыва при крушении.", "option.immersive_aircraft.crashDamage": "Повредить игрока при падении.", "option.immersive_aircraft.preventKillThroughCrash": "Не будет убивать игрока при аварии.", "option.immersive_aircraft.healthBarRow": "Смещай полоску здоровья транспортных средств.", "option.immersive_aircraft.damagePerHealthPoint": "Более высокие значения делают самолеты более прочными.", "option.immersive_aircraft.weaponsAreDestructive": "Разреши некоторым видам оружия разрушать блоки.", "option.immersive_aircraft.dropInventory": "Брось инвентарь, вместо того чтобы сохранять его внутри авиационного предмета.", "option.immersive_aircraft.dropUpgrades": "Сбрасывай апгрейды и кастомизацию, вместо того чтобы сохранять их внутри элемента самолета.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Автоматически восстанавливать здоровье при каждом заданном такте, имитируя ванильное поведение.", "option.immersive_aircraft.requireShiftForRepair": "Ремонтировать только при удержани<PERSON> Shift, в противном случае просто садиться в транспортное средство.", "immersive_aircraft.tooltip.no_target": "Требуется сборка на полу!", "immersive_aircraft.tooltip.no_space": "Не хватает места!", "immersive_aircraft.slot.booster": "Форсированные ракеты", "immersive_aircraft.slot.weapon": "Оружейный слот", "immersive_aircraft.slot.boiler": "Топливная щель", "immersive_aircraft.slot.banner": "Баннерный слот", "immersive_aircraft.slot.dye": "Красильная щель", "immersive_aircraft.slot.upgrade": "Обновление слота", "immersive_aircraft.upgrade.enginespeed": "%s%% мощности двигателя", "immersive_aircraft.upgrade.friction": "%s%% воздушного трения", "immersive_aircraft.upgrade.acceleration": "%s%% взлетной скорости", "immersive_aircraft.upgrade.durability": "%s%% долговечность", "immersive_aircraft.upgrade.fuel": "%s%% потребность в топливе", "immersive_aircraft.upgrade.wind": "%s%% эффект ветра", "immersive_aircraft.upgrade.stabilizer": "%s%% стабилизации", "immersive_aircraft.tooltip.inventory": "Содержит %s предметов."}