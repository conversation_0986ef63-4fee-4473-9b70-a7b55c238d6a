{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersiv Luftfohrt", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Hoch", "key.immersive_aircraft.multi_control_down": "Dal", "key.immersive_aircraft.multi_control_pull": "Tauk Controller", "key.immersive_aircraft.multi_control_push": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_use": "Wapen/Berg benutzen", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Hoch", "key.immersive_aircraft.fallback_control_down": "Dal", "key.immersive_aircraft.fallback_control_pull": "Tauk Controller", "key.immersive_aircraft.fallback_control_push": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_use": "Wapen/Berg benutzen", "key.immersive_aircraft.dismount": "<PERSON>fs<PERSON><PERSON>", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "Lade-Luftschiff", "entity.immersive_aircraft.warship": "Kreegsschip", "entity.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Rump", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Kessel", "item.immersive_aircraft.enhanced_propeller": "Verbesserter Propeller", "item.immersive_aircraft.eco_engine": "Öko Motor", "item.immersive_aircraft.nether_engine": "Nether Motor", "item.immersive_aircraft.steel_boiler": "Stahlkessel", "item.immersive_aircraft.industrial_gears": "Industrielle <PERSON>", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Rumpfverstärkung", "item.immersive_aircraft.improved_landing_gear": "Verbessertes Fahrgestell", "item.immersive_aircraft.rotary_cannon": "Rotationskanone", "item.immersive_aircraft.bomb_bay": "Bombenkammer", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> mit Schietkruut.", "item.immersive_aircraft.bomb_bay.description": "Lett TNT faln, vernichtet keen <PERSON>löck, mäkt abbä söölig Sc<PERSON>en.", "item.immersive_aircraft.telescope.description": "En bulliger Vörsatz vun dat S<PERSON>kglas.", "item.immersive_aircraft.heavy_crossbow.description": "En sware Armbrust mit en kräftigen Schlag, bruuk<PERSON>.", "item.immersive_aircraft.item.upgrade": "Luftfahrtzeuge Upgrade", "item.immersive_aircraft.item.weapon": "Luftfahrtoogwapen", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "Lade-Luftschiff", "item.immersive_aircraft.warship": "Kreegsschip", "item.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Langsam un söölig, mäkt än gansen Speicher.", "item.immersive_aircraft.warship.description": "'n flüggen <PERSON>, lang<PERSON><PERSON>, aver swar bewapent.", "item.immersive_aircraft.biplane.description": "Schnell un vöölig toverlässig. <PERSON><PERSON>, dat diin <PERSON> lang genuch is.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Kraft, weiter so!", "immersive_aircraft.gyrodyne_target_reached": "Minimum Rotorgeschwindigkeit erreicht, bereit zum Abheben!", "immersive_aircraft.invalid_dimension": "Dit Luftfahrtoog funktionert nich in disse Dimension.", "immersive_aircraft.out_of_ammo": "<PERSON>en Mu<PERSON> mehr!", "immersive_aircraft.repair": "%s%% reperiert!", "immersive_aircraft.tried_dismount": "<PERSON><PERSON><PERSON> dr<PERSON>, för uts<PERSON>gen!", "immersive_aircraft.fuel.none": "<PERSON><PERSON>!", "immersive_aircraft.fuel.out": "Dir is de Kraftstoff utgangen!", "immersive_aircraft.fuel.low": "Du hest keen <PERSON><PERSON><PERSON> mehr!", "immersive_aircraft.fat.none": "<PERSON><PERSON>!", "immersive_aircraft.fat.out": "<PERSON> büst to ho<PERSON><PERSON>, för to flügen!", "option.immersive_aircraft.general": "Algemeene Opties", "option.immersive_aircraft.separateCamera": "<PERSON><PERSON> aparte Kamera in Luftfahrtzeuge.", "option.immersive_aircraft.useThirdPersonByDefault": "Stanardmäßig dritte Person Kamera in Luftfahrtzeuge.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON>.", "option.immersive_aircraft.enableAnimatedSails": "Welle Segel.", "option.immersive_aircraft.renderDistance": "Rendern Distanz in Blöcken.", "option.immersive_aircraft.fuelConsumption": "Kraftstoff Verbrauchsrate.", "option.immersive_aircraft.windClearWeather": "Basis Wind Effekt.", "option.immersive_aircraft.windRainWeather": "Wind bei Regenfall.", "option.immersive_aircraft.windThunderWeather": "Extra Wind bei Donner.", "option.immersive_aircraft.repairSpeed": "Reparatur pro Klick.", "option.immersive_aircraft.repairExhaustion": "<PERSON><PERSON>er Utmattung pro Klick.", "option.immersive_aircraft.collisionDamage": "Kollisionsschaden.", "option.immersive_aircraft.burnFuelInCreative": "<PERSON><PERSON><PERSON><PERSON> in de Kreativ-Mode verbrannen.", "option.immersive_aircraft.acceptVanillaFuel": "Normaal Brennstoff akzeptieren.", "option.immersive_aircraft.useCustomKeybindSystem": "Multi-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kann bestimmte Mods kaputt machen.", "option.immersive_aircraft.showHotbarEngineGauge": "De Motoranzeige över de Hotbar anzeigen.", "option.immersive_aircraft.enableCrashExplosion": "Explosion anschalten.", "option.immersive_aircraft.enableCrashBlockDestruction": "Zerstörerische Explosion anschalten.", "option.immersive_aircraft.enableCrashFire": "Feurige Explosion anschalten.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON><PERSON> vun de Crash-Explosion.", "option.immersive_aircraft.crashDamage": "<PERSON>er krig Schaden bi en Crash.", "option.immersive_aircraft.preventKillThroughCrash": "<PERSON><PERSON> de Speler nich dotmaken bi en Crash.", "option.immersive_aircraft.healthBarRow": "Lett de Gesunheitsbalk vun Fahrtoogen.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON> Waarden maken Luftfahrtooge düürbarer.", "option.immersive_aircraft.weaponsAreDestructive": "Lat sommige Wapens Blöcke vernichten.", "option.immersive_aircraft.dropInventory": "Lat de Inventar fallen, anstatt dat innerhalb des Luftfahrt-Items to spekern.", "option.immersive_aircraft.dropUpgrades": "Lat de Verbeterungen un Anpassungen fallen, anstatt dat innerhalb des Luftfahrt-Items to spekern.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatisch verheelt sick alle geewen Ticks, vanilla-<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON><PERSON> nur, wenn Shift gehalten warrt, anners int Fohrteg stiegen.", "immersive_aircraft.tooltip.no_target": "Möt op de Grüntog sammelt warrn!", "immersive_aircraft.tooltip.no_space": "Nich nog Rüm!", "immersive_aircraft.slot.booster": "Boostraketen", "immersive_aircraft.slot.weapon": "Wap<PERSON>lot", "immersive_aircraft.slot.boiler": "Kraftstoffschlitz", "immersive_aircraft.slot.banner": "<PERSON>", "immersive_aircraft.slot.dye": "<PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Upgrade Schlitz", "immersive_aircraft.upgrade.enginespeed": "%s%% Motor Kraft", "immersive_aircraft.upgrade.friction": "%s%% Luftreibung", "immersive_aircraft.upgrade.acceleration": "%s%% Abhebegeschwindigkeit", "immersive_aircraft.upgrade.durability": "%s%% Haltbarkeit", "immersive_aircraft.upgrade.fuel": "%s%% Kraftstoff Bedarf", "immersive_aircraft.upgrade.wind": "%s%% Wind Effekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilisieren", "immersive_aircraft.tooltip.inventory": "Bevat %s Items."}