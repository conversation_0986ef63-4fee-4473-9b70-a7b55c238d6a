{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersiva Aeroplano", "key.immersive_aircraft.multi_control_left": "Sinistra", "key.immersive_aircraft.multi_control_right": "Dextra", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "En retro", "key.immersive_aircraft.multi_control_up": "Sopra", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Tira Kontrolero", "key.immersive_aircraft.multi_control_push": "Puŝa Kontrolero", "key.immersive_aircraft.multi_use": "<PERSON><PERSON> armo/monti", "key.immersive_aircraft.fallback_control_left": "Sinistra", "key.immersive_aircraft.fallback_control_right": "Dextra", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "En retro", "key.immersive_aircraft.fallback_control_up": "Sopra", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Tira Kontrolero", "key.immersive_aircraft.fallback_control_push": "Puŝa Kontrolero", "key.immersive_aircraft.fallback_use": "<PERSON><PERSON> armo/monti", "key.immersive_aircraft.dismount": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON> impulso", "entity.immersive_aircraft.airship": "Aeroporto", "entity.immersive_aircraft.cargo_airship": "Kargo Aeroplano", "entity.immersive_aircraft.warship": "Navalo", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodino", "entity.immersive_aircraft.quadrocopter": "Quadrocoptero", "item.immersive_aircraft.hull": "Nave", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.eco_engine": "Eco Motor", "item.immersive_aircraft.nether_engine": "Nether Motor", "item.immersive_aircraft.steel_boiler": "Steel Boilero", "item.immersive_aircraft.industrial_gears": "Industriala Gears", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroscopio", "item.immersive_aircraft.hull_reinforcement": "Hulla Reinforcement", "item.immersive_aircraft.improved_landing_gear": "Mi<PERSON>orita Landing Gear", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "Bombetru", "item.immersive_aircraft.telescope": "Telescopio", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "Rapidefende kanono kun gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Lande TNT, ne destrue blokoj sed produce pesi dano.", "item.immersive_aircraft.telescope.description": "Un bulkeme versio de la spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "Un pesi krucbogo kun forta puĉo, postulas sagoj.", "item.immersive_aircraft.item.upgrade": "Aeroplano Upgrade", "item.immersive_aircraft.item.weapon": "Aeroplano Arma", "item.immersive_aircraft.airship": "Aeroporto", "item.immersive_aircraft.cargo_airship": "Kargo Aeroplano", "item.immersive_aircraft.warship": "Navalo", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodino", "item.immersive_aircraft.quadrocopter": "Quadrocoptero", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lenta e fuelo-hungre ma porti tuta stratur.", "item.immersive_aircraft.warship.description": "Un fortaleza flante, lenta ma fortemente armata.", "item.immersive_aircraft.biplane.description": "Rapide e rather fiable. Certige ke tua pista es longa suficie.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Potentio, continua pushar!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotoro veloci raggiunt, pronto por takeoff!", "immersive_aircraft.invalid_dimension": "Ti aeroplano ne functione en ti dimension.", "immersive_aircraft.out_of_ammo": "Ek de municio!", "immersive_aircraft.repair": "%s%% reparita!", "immersive_aircraft.tried_dismount": "Premu denove por salti eksteren!", "immersive_aircraft.fuel.none": "Nula fuelo!", "immersive_aircraft.fuel.out": "Tiu es l'una di fuel!", "immersive_aircraft.fuel.low": "Tu es low en fuel!", "immersive_aircraft.fat.none": "<PERSON>ula mangio!", "immersive_aircraft.fat.out": "Tu es tro pro hungry por flyar!", "option.immersive_aircraft.general": "Generala Opzioni", "option.immersive_aircraft.separateCamera": "Usa separata camera en aeroplano.", "option.immersive_aircraft.useThirdPersonByDefault": "Defaulta al tercera persona camera en aeroplano.", "option.immersive_aircraft.enableTrails": "Fantastik steam trails.", "option.immersive_aircraft.enableAnimatedSails": "Ondos sailes.", "option.immersive_aircraft.renderDistance": "Render distanto en bloks.", "option.immersive_aircraft.fuelConsumption": "Fuel consumido.", "option.immersive_aircraft.windClearWeather": "Base vento effecto.", "option.immersive_aircraft.windRainWeather": "Vento in pluve.", "option.immersive_aircraft.windThunderWeather": "Extra vento en thunder.", "option.immersive_aircraft.repairSpeed": "Reparar per klako.", "option.immersive_aircraft.repairExhaustion": "Ezukto de ludisto per klako.", "option.immersive_aircraft.collisionDamage": "Kolizion dano.", "option.immersive_aircraft.burnFuelInCreative": "Burnar fuel en creative modo.", "option.immersive_aircraft.acceptVanillaFuel": "Acceptar vanilla fuel.", "option.immersive_aircraft.useCustomKeybindSystem": "Usar multi-keybindings, povas romper ciertos mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderi la motorgaujo super la hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Aktive esplosione.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktive destruktive esplosione.", "option.immersive_aircraft.enableCrashFire": "Aktive incendio esplosione.", "option.immersive_aircraft.crashExplosionRadius": "Dimensio de kolapsa esplosione.", "option.immersive_aircraft.crashDamage": "Danos la ludanto en un kolapso.", "option.immersive_aircraft.preventKillThroughCrash": "Ne morti la ludanto en un kolapso.", "option.immersive_aircraft.healthBarRow": "Offsetti la sanbaro de vehiculos.", "option.immersive_aircraft.damagePerHealthPoint": "Pli altas valoroj face aeroplanos plu durable.", "option.immersive_aircraft.weaponsAreDestructive": "<PERSON><PERSON>i kelka armamenti por destrui blo<PERSON>.", "option.immersive_aircraft.dropInventory": "Lansu la inventario, anstataŭ ke konservi ĝin en la aeron.", "option.immersive_aircraft.dropUpgrades": "Lansu la plibonigon e personalizadon, anstataŭ ke konservi ĝin en la aeron.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Aŭtomate regeneras sanon ĉiu donita intervalo, simulante vanilan konduton.", "option.immersive_aircraft.requireShiftForRepair": "Nur riparu kiam tenante ŝift, alie simple eniru la veturilon.", "immersive_aircraft.tooltip.no_target": "Deve asi sur la planko!", "immersive_aircraft.tooltip.no_space": "Ne sufiĉe spac!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "immersive_aircraft.slot.weapon": "<PERSON><PERSON> fako", "immersive_aircraft.slot.boiler": "Fuelslote", "immersive_aircraft.slot.banner": "<PERSON> slote", "immersive_aircraft.slot.dye": "<PERSON><PERSON> slote", "immersive_aircraft.slot.upgrade": "Upgrade slote", "immersive_aircraft.upgrade.enginespeed": "%s%% motor potentio", "immersive_aircraft.upgrade.friction": "%s%% aero frictio", "immersive_aircraft.upgrade.acceleration": "%s%% takeoff veloci", "immersive_aircraft.upgrade.durability": "%s%% durabilita", "immersive_aircraft.upgrade.fuel": "%s%% fuel requerimiento", "immersive_aircraft.upgrade.wind": "%s%% vento effecto", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizante", "immersive_aircraft.tooltip.inventory": "Contene %s artikli."}