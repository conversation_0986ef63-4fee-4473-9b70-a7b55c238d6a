{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Flugtechnologie", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Vorwärts", "key.immersive_aircraft.multi_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Hoch", "key.immersive_aircraft.multi_control_down": "<PERSON>ter", "key.immersive_aircraft.multi_control_pull": "Steuerhebel z<PERSON>", "key.immersive_aircraft.multi_control_push": "Steuerhebel drücken", "key.immersive_aircraft.multi_use": "Waffe/Mount verwenden", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Vorwärts", "key.immersive_aircraft.fallback_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Hoch", "key.immersive_aircraft.fallback_control_down": "<PERSON>ter", "key.immersive_aircraft.fallback_control_pull": "Steuerhebel z<PERSON>", "key.immersive_aircraft.fallback_control_push": "Steuerhebel drücken", "key.immersive_aircraft.fallback_use": "Waffe/Mount verwenden", "key.immersive_aircraft.dismount": "Absteigen", "key.immersive_aircraft.boost": "Raketenschub", "entity.immersive_aircraft.airship": "Luftschiff", "entity.immersive_aircraft.cargo_airship": "Frachtluftschiff", "entity.immersive_aircraft.warship": "Kriegsschiff", "entity.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Flugschrauber", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Triebwerk", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Heizkessel", "item.immersive_aircraft.enhanced_propeller": "Verbesserter Propeller", "item.immersive_aircraft.eco_engine": "Öko-Motor", "item.immersive_aircraft.nether_engine": "Nether-Motor", "item.immersive_aircraft.steel_boiler": "Stahlkessel", "item.immersive_aircraft.industrial_gears": "Industriegetriebe", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Rumpfverstärkung", "item.immersive_aircraft.improved_landing_gear": "Verbessertes Fahrwerk", "item.immersive_aircraft.rotary_cannon": "Drehrohrkanone", "item.immersive_aircraft.bomb_bay": "Bombenbucht", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Schwere Armbrust", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON>uer<PERSON><PERSON>, die mit Schießpulver betrieben wird.", "item.immersive_aircraft.bomb_bay.description": "Wirft TNT ab, zerstört keine Blöcke, verursacht aber großen Schaden.", "item.immersive_aircraft.telescope.description": "Eine größere Version des Fernglases.", "item.immersive_aircraft.heavy_crossbow.description": "Eine schwere Armbrust mit einem starken Durchschlag, für den Pfeile benötigt werden.", "item.immersive_aircraft.item.upgrade": "Upgrade des Flugzeugs", "item.immersive_aircraft.item.weapon": "Flugzeugwaffe", "item.immersive_aircraft.airship": "Luftschiff", "item.immersive_aircraft.cargo_airship": "Frachtluftschiff", "item.immersive_aircraft.warship": "Kriegsschiff", "item.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Flugschrauber", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Luftschiffe sind vielleicht nicht die schnellsten Fahrzeuge, aber sie sind leicht zu manövrieren.", "item.immersive_aircraft.cargo_airship.description": "Langsam und treibstoffhungrig, aber er transportiert ein ganzes <PERSON>ger.", "item.immersive_aircraft.warship.description": "Eine fliegende Festung, lang<PERSON><PERSON>, aber schwer bewaffnet.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON><PERSON> und ziemlich zuverlässig. Achte da<PERSON>, dass deine Landebahn lang genug ist.", "item.immersive_aircraft.gyrodyne.description": "Wer braucht schon einen Motor, wenn man das Flugzeug mit reiner Muskelkraft antreiben kann? Gib ihm einen kräftigen Schubs und schon fliegt es los!", "item.immersive_aircraft.quadrocopter.description": "Ein Meisterwerk der Technik! 4 Rotoren, die auf ein Stück Bambus geschnallt sind. Perfekt zum Bauen, und das war's auch schon.", "immersive_aircraft.gyrodyne_target": "%d%% energie, schieb weiter!", "immersive_aircraft.gyrodyne_target_reached": "Der Propellers läuft, bereit zum Abheben!", "immersive_aircraft.invalid_dimension": "Dieses Flugzeug funktioniert nicht in dieser Dimension.", "immersive_aircraft.out_of_ammo": "<PERSON><PERSON> Mu<PERSON> mehr!", "immersive_aircraft.repair": "%s%% repariert!", "immersive_aircraft.tried_dismount": "<PERSON><PERSON><PERSON> erneut, um herauszu<PERSON>en!", "immersive_aircraft.fuel.none": "<PERSON><PERSON>!", "immersive_aircraft.fuel.out": "Dir ist der Brennstoff ausgegangen!", "immersive_aircraft.fuel.low": "Du hast nur noch wenig Brennstoff!", "immersive_aircraft.fat.none": "<PERSON><PERSON>!", "immersive_aircraft.fat.out": "Du bist zu hungrig zum Fliegen!", "option.immersive_aircraft.general": "Allgemeine Optionen", "option.immersive_aircraft.separateCamera": "Verwende eine separate Kamera im Flugzeug.", "option.immersive_aircraft.useThirdPersonByDefault": "Standardmäßig ist die Kamera im Flugzeug in der dritten Person.", "option.immersive_aircraft.enableTrails": "Kondensstreifen.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.renderDistance": "Renderabstand in Blöcken.", "option.immersive_aircraft.fuelConsumption": "Kraftstoffverbrennungsgeschwindigkeit.", "option.immersive_aircraft.windClearWeather": "Basis-Wind-Effekt.", "option.immersive_aircraft.windRainWeather": "Wind bei Niederschlag.", "option.immersive_aircraft.windThunderWeather": "Extra Wind bei Gewitter.", "option.immersive_aircraft.repairSpeed": "Reparatur pro Klick.", "option.immersive_aircraft.repairExhaustion": "Spielererschöpfung pro Klick.", "option.immersive_aircraft.collisionDamage": "Kollisionsschaden.", "option.immersive_aircraft.burnFuelInCreative": "Verbrenne Treibstoff im Kreativmodus.", "option.immersive_aircraft.acceptVanillaFuel": "Akzeptiere Vanilla-Kraftstoff.", "option.immersive_aircraft.useCustomKeybindSystem": "Verwende Mehrfach-Tastenbelegungen, dies kann aber andere Mods beeinflussen!", "option.immersive_aircraft.showHotbarEngineGauge": "Rendere die Motoranzeige über die Hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Aktiviere die Explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktiviere die zerstörerische Explosion.", "option.immersive_aircraft.enableCrashFire": "Aktiviere die feurige Explosion.", "option.immersive_aircraft.crashExplosionRadius": "Größe der Crash-Explosion.", "option.immersive_aircraft.crashDamage": "Beschädige den Spieler bei einem Absturz.", "option.immersive_aircraft.preventKillThroughCrash": "Der Spieler wird bei einem Absturz nicht getötet.", "option.immersive_aircraft.healthBarRow": "Versetze die Gesundheitsleiste von Fahrzeugen.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON>here Werte machen Flugzeuge langlebiger.", "option.immersive_aircraft.weaponsAreDestructive": "Erlaube einigen W<PERSON>fen, <PERSON><PERSON><PERSON><PERSON> zu zerstören.", "option.immersive_aircraft.dropInventory": "Lass das Inventar fallen, anstatt es im Flugzeugobjekt zu speichern.", "option.immersive_aircraft.dropUpgrades": "Lass die Upgrades und Anpassungen weg, anstatt sie im Flugzeugobjekt zu speichern.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneriert die Gesundheit automatisch jeden Tick, um das Vanilla-Verhalten zu simulieren.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON>ere nur, wenn du den Schaltknüppel hä<PERSON>t, an<PERSON>ten steig einfach in das Fahrzeug ein.", "immersive_aircraft.tooltip.no_target": "Muss auf dem Boden montiert werden!", "immersive_aircraft.tooltip.no_space": "Nicht genug Platz!", "immersive_aircraft.slot.booster": "Boost-Raketen", "immersive_aircraft.slot.weapon": "Waffenplatz", "immersive_aircraft.slot.boiler": "Kraftstoff-Slot", "immersive_aircraft.slot.banner": "Banner-Slot", "immersive_aircraft.slot.dye": "Farb-Slot", "immersive_aircraft.slot.upgrade": "Upgrade-Slot", "immersive_aircraft.upgrade.enginespeed": "%s%% Motorleistung", "immersive_aircraft.upgrade.friction": "%s%% Luftreibung", "immersive_aircraft.upgrade.acceleration": "%s%% Abfluggeschwindigkeit", "immersive_aircraft.upgrade.durability": "%s%% Haltbarkeit", "immersive_aircraft.upgrade.fuel": "%s%% Brennstoffbedarf", "immersive_aircraft.upgrade.wind": "%s%% Windeffekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilisierend", "immersive_aircraft.tooltip.inventory": "Enthält %s Artikel."}