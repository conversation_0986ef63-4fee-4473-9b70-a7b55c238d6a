{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aeronaves Imersivas", "key.immersive_aircraft.multi_control_left": "E<PERSON>rda", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Voltar", "key.immersive_aircraft.multi_control_up": "Para cima", "key.immersive_aircraft.multi_control_down": "Para baixo", "key.immersive_aircraft.multi_control_pull": "Controlador de tração", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Usar arma/montaria", "key.immersive_aircraft.fallback_control_left": "E<PERSON>rda", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Voltar", "key.immersive_aircraft.fallback_control_up": "Para cima", "key.immersive_aircraft.fallback_control_down": "Para baixo", "key.immersive_aircraft.fallback_control_pull": "Controlador de tração", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Usar arma/montaria", "key.immersive_aircraft.dismount": "Desmontar", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "Aeróstato de carga", "entity.immersive_aircraft.warship": "Navio <PERSON>", "entity.immersive_aircraft.biplane": "Avião Biplano", "entity.immersive_aircraft.gyrodyne": "<PERSON>dino", "entity.immersive_aircraft.quadrocopter": "Quadricóptero", "item.immersive_aircraft.hull": "Casco", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "<PERSON>deira", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.eco_engine": "Motor Ecológico", "item.immersive_aircraft.nether_engine": "Motor Nether", "item.immersive_aircraft.steel_boiler": "Caldeira de aço", "item.immersive_aircraft.industrial_gears": "Artes industriais", "item.immersive_aircraft.sturdy_pipes": "Tubos robustos", "item.immersive_aircraft.gyroscope": "Giroscópio", "item.immersive_aircraft.hull_reinforcement": "Reforço do casco", "item.immersive_aircraft.improved_landing_gear": "Trem de aterragem melhorado", "item.immersive_aircraft.rotary_cannon": "Canhão giratório", "item.immersive_aircraft.bomb_bay": "Baía de bombas", "item.immersive_aircraft.telescope": "Telescópio", "item.immersive_aircraft.heavy_crossbow": "Besta pesada", "item.immersive_aircraft.rotary_cannon.description": "Canhão de disparo rápido que funciona com pólvora.", "item.immersive_aircraft.bomb_bay.description": "Derruba TNT, não destrói blocos, mas causa muito dano.", "item.immersive_aircraft.telescope.description": "<PERSON>a versão mais volumosa da luneta.", "item.immersive_aircraft.heavy_crossbow.description": "Uma besta pesada com um soco poderoso, que requer flechas.", "item.immersive_aircraft.item.upgrade": "Upgrade de Aeronaves", "item.immersive_aircraft.item.weapon": "Arma da aeronave", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "Aeróstato de carga", "item.immersive_aircraft.warship": "Navio <PERSON>", "item.immersive_aircraft.biplane": "Avião Biplano", "item.immersive_aircraft.gyrodyne": "<PERSON>dino", "item.immersive_aircraft.quadrocopter": "Quadricóptero", "item.immersive_aircraft.airship.description": "<PERSON><PERSON>, mas fáci<PERSON> de man<PERSON>.", "item.immersive_aircraft.cargo_airship.description": "É lento e consome muito combustível, mas transporta todo um armazém.", "item.immersive_aircraft.warship.description": "Uma fortaleza voadora, lenta, mas fortemente armada.", "item.immersive_aircraft.biplane.description": "Rápido e bastante confiável. Certifique-se de que a sua pista é realmente longa.", "item.immersive_aircraft.gyrodyne.description": "Quem precisa de um motor se se pode alimentar a aeronave com músculos puros? Dêem-lhe um bom empurrão e saiam voando!", "item.immersive_aircraft.quadrocopter.description": "Uma obra-prima de engenharia! 4 rotores amarrados a algum bambu. Perfeito para a construção, e é só isso.", "immersive_aircraft.gyrodyne_target": "%d%% De força, continue empurrando!", "immersive_aircraft.gyrodyne_target_reached": "Velocidade mínima de rotor atingida, pronto para decolagem!", "immersive_aircraft.invalid_dimension": "Este avião não funciona nesta dimensão.", "immersive_aircraft.out_of_ammo": "Sem munição!", "immersive_aircraft.repair": "%s%% reparado!", "immersive_aircraft.tried_dismount": "Pressione novamente para pular!", "immersive_aircraft.fuel.none": "Sem combustível!", "immersive_aircraft.fuel.out": "Sem combustível!", "immersive_aircraft.fuel.low": "Combustível baixo!", "immersive_aircraft.fat.none": "Sem comida!", "immersive_aircraft.fat.out": "<PERSON>ito faminto para voar!", "option.immersive_aircraft.general": "Opções gerais", "option.immersive_aircraft.separateCamera": "Utilizar uma câmara fotográfica separada nos aviões.", "option.immersive_aircraft.useThirdPersonByDefault": "O defeito é a câmara de terceira pessoa em aviões.", "option.immersive_aircraft.enableTrails": "Pistas de vapor extravagantes.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON> on<PERSON>.", "option.immersive_aircraft.renderDistance": "Distância de renderização em blocos.", "option.immersive_aircraft.fuelConsumption": "Taxa de queima de combustível.", "option.immersive_aircraft.windClearWeather": "Efeito do vento de base.", "option.immersive_aircraft.windRainWeather": "Vento ao cair da chuva.", "option.immersive_aircraft.windThunderWeather": "Vento extra ao trovão.", "option.immersive_aircraft.repairSpeed": "Reparo por clique.", "option.immersive_aircraft.repairExhaustion": "Exaustão do jogador por clique.", "option.immersive_aircraft.collisionDamage": "Danos por colisão.", "option.immersive_aircraft.burnFuelInCreative": "Queimar combustível em modo criativo.", "option.immersive_aircraft.acceptVanillaFuel": "Aceitar combustível de baunilha.", "option.immersive_aircraft.useCustomKeybindSystem": "Utilizar ligações multi-keybindings, pode quebrar certos mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderize o medidor do motor sobre a hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Habilitar explosão.", "option.immersive_aircraft.enableCrashBlockDestruction": "Permite a explosão destrutiva.", "option.immersive_aircraft.enableCrashFire": "Permitir explos<PERSON> de <PERSON>o.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON> da explosão do acidente.", "option.immersive_aircraft.crashDamage": "Danificar o jogador num acidente.", "option.immersive_aircraft.preventKillThroughCrash": "Não mata o jogador em caso de colisão.", "option.immersive_aircraft.healthBarRow": "Compensa a barra de saúde dos veículos.", "option.immersive_aircraft.damagePerHealthPoint": "Valores mais altos tornam a aeronave mais durável.", "option.immersive_aircraft.weaponsAreDestructive": "Permitir que algumas armas destruam blocos.", "option.immersive_aircraft.dropInventory": "Solte o inventário, em vez de salvá-lo no item da aeronave.", "option.immersive_aircraft.dropUpgrades": "Elimine as atualizações e a personalização, em vez de salvá-las no item da aeronave.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenera automaticamente a saúde a cada tique, simulando o comportamento do modelo original.", "option.immersive_aircraft.requireShiftForRepair": "Repare somente quando você estiver segurando o câmbio, caso contr<PERSON><PERSON>, entre no veículo.", "immersive_aircraft.tooltip.no_target": "Você precisa ser montado no chão!", "immersive_aircraft.tooltip.no_space": "Você não tem espaço suficiente!", "immersive_aircraft.slot.booster": "Impulsionar fogue<PERSON>", "immersive_aircraft.slot.weapon": "Slot para armas", "immersive_aircraft.slot.boiler": "Slot de combustível", "immersive_aircraft.slot.banner": "<PERSON><PERSON> de estandarte", "immersive_aircraft.slot.dye": "<PERSON>lot de tinta", "immersive_aircraft.slot.upgrade": "<PERSON>lot de melhoria", "immersive_aircraft.upgrade.enginespeed": "%s%% potência do motor", "immersive_aircraft.upgrade.friction": "%s%% fricção de ar", "immersive_aircraft.upgrade.acceleration": "%s%% velocidade de decolagem", "immersive_aircraft.upgrade.durability": "%s%% durabilidade", "immersive_aircraft.upgrade.fuel": "%s%% necessidade de combustível", "immersive_aircraft.upgrade.wind": "%s%% efeito do vento", "immersive_aircraft.upgrade.stabilizer": "%s%% estabilização", "immersive_aircraft.tooltip.inventory": "Contém %s itens."}