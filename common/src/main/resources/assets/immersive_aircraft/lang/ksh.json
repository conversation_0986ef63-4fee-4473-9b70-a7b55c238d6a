{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Flugtechnologie", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Vorwärts", "key.immersive_aircraft.multi_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Hoch", "key.immersive_aircraft.multi_control_down": "<PERSON>ter", "key.immersive_aircraft.multi_control_pull": "Steuerhebel z<PERSON>", "key.immersive_aircraft.multi_control_push": "Steuerhebel drücken", "key.immersive_aircraft.multi_use": "<PERSON><PERSON><PERSON>/Reittier ben<PERSON>en", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Vorwärts", "key.immersive_aircraft.fallback_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Hoch", "key.immersive_aircraft.fallback_control_down": "<PERSON>ter", "key.immersive_aircraft.fallback_control_pull": "Steuerhebel z<PERSON>", "key.immersive_aircraft.fallback_control_push": "Steuerhebel drücken", "key.immersive_aircraft.fallback_use": "<PERSON><PERSON><PERSON>/Reittier ben<PERSON>en", "key.immersive_aircraft.dismount": "Absteigen", "key.immersive_aircraft.boost": "Raketen-Boost", "entity.immersive_aircraft.airship": "Luftschiff", "entity.immersive_aircraft.cargo_airship": "Cargo Luftschiff", "entity.immersive_aircraft.warship": "Kriegsschiff", "entity.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Flugschrauber", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Triebwerk", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Heizkessel", "item.immersive_aircraft.enhanced_propeller": "Verbesserter Propeller", "item.immersive_aircraft.eco_engine": "Öko-Motor", "item.immersive_aircraft.nether_engine": "Nether-Motor", "item.immersive_aircraft.steel_boiler": "Stahl-Heizkessel", "item.immersive_aircraft.industrial_gears": "Industrielle <PERSON>", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Rumpfverstärkung", "item.immersive_aircraft.improved_landing_gear": "Verbessertes Fahrwerk", "item.immersive_aircraft.rotary_cannon": "Rotationskanone", "item.immersive_aircraft.bomb_bay": "Bombenbucht", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Schwere Armbrust", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die mit Schießpulver funktioniert.", "item.immersive_aircraft.bomb_bay.description": "Wirft TNT, zerstört keine Blöcke, fügt jedoch schweren Schaden zu.", "item.immersive_aircraft.telescope.description": "Eine massigere Version des Fernrohrs.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>e schwere Armbrust mit einem kräftigen Schlag, <PERSON><PERSON><PERSON><PERSON>.", "item.immersive_aircraft.item.upgrade": "Flugzeug-Upgrade", "item.immersive_aircraft.item.weapon": "Flugzeugwaffe", "item.immersive_aircraft.airship": "Luftschiff", "item.immersive_aircraft.cargo_airship": "Cargo Luftschiff", "item.immersive_aircraft.warship": "Kriegsschiff", "item.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Flugschrauber", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Langsam und benzinverschwenderisch aber trägt einen ganzen Lagerraum.", "item.immersive_aircraft.warship.description": "E flotte Festung, langsam aber schwer bewaffnet.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON><PERSON> und recht zuverlässig. <PERSON><PERSON> sic<PERSON>, dass deine Landebahn lang genug ist.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% energie, schieb weiter!", "immersive_aircraft.gyrodyne_target_reached": "Der Propellers läuft, bereit zum Abheben!", "immersive_aircraft.invalid_dimension": "Dieses Flugzeug funktioniert in dieser Dimension nicht.", "immersive_aircraft.out_of_ammo": "Kein <PERSON>!", "immersive_aircraft.repair": "%s%% repariert!", "immersive_aircraft.tried_dismount": "<PERSON><PERSON><PERSON> erneut, um auszusteigen!", "immersive_aircraft.fuel.none": "<PERSON><PERSON>!", "immersive_aircraft.fuel.out": "Dir ist der Brennstoff ausgegangen!", "immersive_aircraft.fuel.low": "Du hast nur noch wenig Brennstoff!", "immersive_aircraft.fat.none": "<PERSON><PERSON>!", "immersive_aircraft.fat.out": "Du bist zu hungrig, um zu fliegen!", "option.immersive_aircraft.general": "Allgemeine Optionen", "option.immersive_aircraft.separateCamera": "Verwende eine separate Kamera im Flugzeug.", "option.immersive_aircraft.useThirdPersonByDefault": "Standardmäßig auf die Third-Person-Kamera im Flugzeug.", "option.immersive_aircraft.enableTrails": "Kondensstreifen.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.renderDistance": "Renderdistanz in Blöcken.", "option.immersive_aircraft.fuelConsumption": "Kraftstoffverbrauch.", "option.immersive_aircraft.windClearWeather": "Basis-Wind-Effekt.", "option.immersive_aircraft.windRainWeather": "Wind bei Regen.", "option.immersive_aircraft.windThunderWeather": "Extra Wind bei Gewitter.", "option.immersive_aircraft.repairSpeed": "Reparatur pro Klick.", "option.immersive_aircraft.repairExhaustion": "Spieler erschöpfen pro Klick.", "option.immersive_aircraft.collisionDamage": "Kollisionsschaden.", "option.immersive_aircraft.burnFuelInCreative": "Brenne Kraftstoff im Kreativmodus.", "option.immersive_aircraft.acceptVanillaFuel": "Akzeptiere Vanilla-Kraftstoff.", "option.immersive_aircraft.useCustomKeybindSystem": "Verwende Mehrfach-Tastenbelegungen, dies kann aber andere Mods beeinflussen.", "option.immersive_aircraft.showHotbarEngineGauge": "Zeige das Motorenmessgerät über der Hotbar an.", "option.immersive_aircraft.enableCrashExplosion": "Explosion aktivieren.", "option.immersive_aircraft.enableCrashBlockDestruction": "Zerstörerische Explosion aktivieren.", "option.immersive_aircraft.enableCrashFire": "Feurige Explosion aktivieren.", "option.immersive_aircraft.crashExplosionRadius": "Größe der Absturzexplosion.", "option.immersive_aircraft.crashDamage": "Schaden dem Spieler bei einem Absturz.", "option.immersive_aircraft.preventKillThroughCrash": "Wird den Spieler bei einem Absturz nicht töten.", "option.immersive_aircraft.healthBarRow": "Gesundheitsanzeige von Fahrzeugen <PERSON>en.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON> We<PERSON> machen Flugzeuge haltbarer.", "option.immersive_aircraft.weaponsAreDestructive": "Erlaube einige Waffen, <PERSON><PERSON><PERSON><PERSON> zu zerstören.", "option.immersive_aircraft.dropInventory": "Lass das Inventar fallen, anstatt es innerhalb des Flugzeugitems zu speichern.", "option.immersive_aircraft.dropUpgrades": "Lass die Upgrades und Anpassungen fallen, anstatt sie innerhalb des Flugzeugitems zu speichern.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneriert automatisch Gesundheit in jedem angegebenen Takt, simuliert Vanille-Verhalten.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON> reparieren, wenn du die Shift-Taste gedr<PERSON>t hältst, andernfalls nur das Fahrzeug betreten.", "immersive_aircraft.tooltip.no_target": "Muss auf dem Boden zusammengebaut werden!", "immersive_aircraft.tooltip.no_space": "Nicht genug Platz!", "immersive_aircraft.slot.booster": "Boost-Raketen", "immersive_aircraft.slot.weapon": "Waffenplatz", "immersive_aircraft.slot.boiler": "Kraftstoff-Slot", "immersive_aircraft.slot.banner": "Banner-Slot", "immersive_aircraft.slot.dye": "Farb-Slot", "immersive_aircraft.slot.upgrade": "Upgrade-Slot", "immersive_aircraft.upgrade.enginespeed": "%s%% Motorleistung", "immersive_aircraft.upgrade.friction": "%s%% Luftreibung", "immersive_aircraft.upgrade.acceleration": "%s%% Startgeschwindigkeit", "immersive_aircraft.upgrade.durability": "%s%% Haltbarkeit", "immersive_aircraft.upgrade.fuel": "%s%% Brennstoffbedarf", "immersive_aircraft.upgrade.wind": "%s%% Windeffekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilisieren", "immersive_aircraft.tooltip.inventory": "Enthält %s Artikel."}