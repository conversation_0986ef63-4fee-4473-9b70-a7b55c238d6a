{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Захватывающая Авиация", "key.immersive_aircraft.multi_control_left": "Влево", "key.immersive_aircraft.multi_control_right": "Вправо", "key.immersive_aircraft.multi_control_forward": "Вперёд", "key.immersive_aircraft.multi_control_backward": "Назад", "key.immersive_aircraft.multi_control_up": "Ввер<PERSON>", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Тангаж вверх", "key.immersive_aircraft.multi_control_push": "Тангаж вниз", "key.immersive_aircraft.multi_use": "Использовать оружие/оборудование", "key.immersive_aircraft.fallback_control_left": "Влево", "key.immersive_aircraft.fallback_control_right": "Вправо", "key.immersive_aircraft.fallback_control_forward": "Вперёд", "key.immersive_aircraft.fallback_control_backward": "Назад", "key.immersive_aircraft.fallback_control_up": "Ввер<PERSON>", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Тангаж вверх", "key.immersive_aircraft.fallback_control_push": "Тангаж вниз", "key.immersive_aircraft.fallback_use": "Использовать оружие/оборудование", "key.immersive_aircraft.dismount": "Высадиться", "key.immersive_aircraft.boost": "Разгонные ракеты", "entity.immersive_aircraft.airship": "Ди<PERSON><PERSON>ж<PERSON>бль", "entity.immersive_aircraft.cargo_airship": "Грузовой дирижабль", "entity.immersive_aircraft.warship": "Военный дирижабль", "entity.immersive_aircraft.biplane": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Винтокрыл", "entity.immersive_aircraft.quadrocopter": "Квадрокоптер", "item.immersive_aircraft.hull": "Корпус", "item.immersive_aircraft.engine": "Двигатель", "item.immersive_aircraft.sail": "<PERSON>а<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Пропеллер", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Усовершенствованный пропеллер", "item.immersive_aircraft.eco_engine": "Экодвигатель", "item.immersive_aircraft.nether_engine": "Незерский двигатель", "item.immersive_aircraft.steel_boiler": "Стальной бойлер", "item.immersive_aircraft.industrial_gears": "Промышленные механизмы", "item.immersive_aircraft.sturdy_pipes": "Прочные трубы", "item.immersive_aircraft.gyroscope": "Гироскоп", "item.immersive_aircraft.hull_reinforcement": "Усиление корпуса", "item.immersive_aircraft.improved_landing_gear": "Улучшенное шасси", "item.immersive_aircraft.rotary_cannon": "Вращающаяся пушка", "item.immersive_aircraft.bomb_bay": "Бомбовый отсек", "item.immersive_aircraft.telescope": "Телескоп", "item.immersive_aircraft.heavy_crossbow": "Тяж<PERSON><PERSON><PERSON>й арбалет", "item.immersive_aircraft.rotary_cannon.description": "Скорострельная пушка, работающая на порохе.", "item.immersive_aircraft.bomb_bay.description": "Сбрасывает динамит, не разрушает блоки, но наносит большой урон.", "item.immersive_aircraft.telescope.description": "Более громоздкая версия подзорной трубы.", "item.immersive_aircraft.heavy_crossbow.description": "Тяжёлый арбалет с мощным ударом, требует стрелы.", "item.immersive_aircraft.item.upgrade": "Модернизация летательного аппарата", "item.immersive_aircraft.item.weapon": "Авиационное оружие", "item.immersive_aircraft.airship": "Ди<PERSON><PERSON>ж<PERSON>бль", "item.immersive_aircraft.cargo_airship": "Грузовой дирижабль", "item.immersive_aircraft.warship": "Военный дирижабль", "item.immersive_aircraft.biplane": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Винтокрыл", "item.immersive_aircraft.quadrocopter": "Квадрокоптер", "item.immersive_aircraft.airship.description": "Медленный, но лёгкий в маневрировании.", "item.immersive_aircraft.cargo_airship.description": "Медлителен и требует много топлива, но вмещает целый склад.", "item.immersive_aircraft.warship.description": "Летающая крепость, медлительная, но тяжеловооружённая.", "item.immersive_aircraft.biplane.description": "Быстрый и достаточно надёжный. Убедитесь, что ваша взлётно-посадочная полоса достаточно длинная.", "item.immersive_aircraft.gyrodyne.description": "Вертолёт с мускульным приводом. Хорошенько толкните его, и он взлетит!", "item.immersive_aircraft.quadrocopter.description": "Идеален для строительства, и это всё.", "immersive_aircraft.gyrodyne_target": "%d%% энергии, продолжайте!", "immersive_aircraft.gyrodyne_target_reached": "Достигнута минимальная частота вращения несущего винта, готов к взлёту!", "immersive_aircraft.invalid_dimension": "Этот летательный аппарат не работает в этом измерении.", "immersive_aircraft.out_of_ammo": "Закончились боеприпасы!", "immersive_aircraft.repair": "%s%% отремонтировано!", "immersive_aircraft.tried_dismount": "Нажмите ещё раз, чтобы выпрыгнуть!", "immersive_aircraft.fuel.none": "Нет топлива!", "immersive_aircraft.fuel.out": "У вас закончилось топливо!", "immersive_aircraft.fuel.low": "У вас мало топлива!", "immersive_aircraft.fat.none": "Нет еды!", "immersive_aircraft.fat.out": "Вы слишком голодны, чтобы летать!", "option.immersive_aircraft.general": "Общие параметры", "option.immersive_aircraft.separateCamera": "Использовать отдельную камеру в летательном аппарате.", "option.immersive_aircraft.useThirdPersonByDefault": "По умолчанию использовать в летательном аппарате камеру от третьего лица.", "option.immersive_aircraft.enableTrails": "Причудливые паровые следы.", "option.immersive_aircraft.enableAnimatedSails": "Волнующиеся паруса.", "option.immersive_aircraft.renderDistance": "Отображать расстояние в блоках.", "option.immersive_aircraft.fuelConsumption": "Скорость сгорания топлива.", "option.immersive_aircraft.windClearWeather": "Базовый эффект ветра.", "option.immersive_aircraft.windRainWeather": "Ветер во время дождя.", "option.immersive_aircraft.windThunderWeather": "Дополнительный ветер при грозе.", "option.immersive_aircraft.repairSpeed": "Ремонт за клик.", "option.immersive_aircraft.repairExhaustion": "Истощение игрока за клик.", "option.immersive_aircraft.collisionDamage": "Повреждения при столкновении.", "option.immersive_aircraft.burnFuelInCreative": "Сжигать топливо в творческом режиме.", "option.immersive_aircraft.acceptVanillaFuel": "Принимать ванильное топливо.", "option.immersive_aircraft.useCustomKeybindSystem": "Использовать сочетания привязок клавиш, это может нарушить работу некоторых модов.", "option.immersive_aircraft.showHotbarEngineGauge": "Отображать индикатор двигателя над панелью быстрого доступа.", "option.immersive_aircraft.enableCrashExplosion": "Включить взрыв.", "option.immersive_aircraft.enableCrashBlockDestruction": "Включить разрушительный взрыв.", "option.immersive_aircraft.enableCrashFire": "Включить огненный взрыв.", "option.immersive_aircraft.crashExplosionRadius": "Размер взрыва при крушении.", "option.immersive_aircraft.crashDamage": "Наносить урон игроку при крушении.", "option.immersive_aircraft.preventKillThroughCrash": "Не убивать игрока при крушении.", "option.immersive_aircraft.healthBarRow": "Смещать полосу здоровья транспортных средств.", "option.immersive_aircraft.damagePerHealthPoint": "Более высокие значения делают летательные аппараты более прочными.", "option.immersive_aircraft.weaponsAreDestructive": "Разрешить некоторым видам оружия разрушать блоки.", "option.immersive_aircraft.dropInventory": "Выбрасывать инвентарь, вместо того чтобы сохранять его внутри предмета летательного аппарата.", "option.immersive_aircraft.dropUpgrades": "Выбрасывать модернизации и кастомизации, вместо того чтобы сохранять их внутри предмета летательного аппарата.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Автоматически восстанавливать здоровье при каждом заданном такте, имитируя ванильное поведение.", "option.immersive_aircraft.requireShiftForRepair": "Ремонтировать только при удержани<PERSON> Shift, в противном случае просто садиться в транспортное средство.", "immersive_aircraft.tooltip.no_target": "Требуется сборка на полу!", "immersive_aircraft.tooltip.no_space": "Не хватает места!", "immersive_aircraft.slot.booster": "Разгонные ракеты", "immersive_aircraft.slot.weapon": "Оружие", "immersive_aircraft.slot.boiler": "Топливо", "immersive_aircraft.slot.banner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.dye": "Краска", "immersive_aircraft.slot.upgrade": "Модернизация", "immersive_aircraft.upgrade.enginespeed": "%s%% мощности двигателя", "immersive_aircraft.upgrade.friction": "%s%% трения о воздух", "immersive_aircraft.upgrade.acceleration": "%s%% взлётной скорости", "immersive_aircraft.upgrade.durability": "%s%% прочности", "immersive_aircraft.upgrade.fuel": "%s%% потребности в топливе", "immersive_aircraft.upgrade.wind": "%s%% эффекта ветра", "immersive_aircraft.upgrade.stabilizer": "%s%% стабилизации", "immersive_aircraft.tooltip.inventory": "Содержит %s предметов."}