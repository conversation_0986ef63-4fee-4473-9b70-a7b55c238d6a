{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "Left", "key.immersive_aircraft.multi_control_right": "Right", "key.immersive_aircraft.multi_control_forward": "Forward", "key.immersive_aircraft.multi_control_backward": "Backward", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Use weapon/mount", "key.immersive_aircraft.fallback_control_left": "Left", "key.immersive_aircraft.fallback_control_right": "Right", "key.immersive_aircraft.fallback_control_forward": "Forward", "key.immersive_aircraft.fallback_control_backward": "Backward", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Use weapon/mount", "key.immersive_aircraft.dismount": "Dismount", "key.immersive_aircraft.boost": "Rocket boost", "entity.immersive_aircraft.airship": "Airship", "entity.immersive_aircraft.cargo_airship": "Cargo Airship", "entity.immersive_aircraft.warship": "Warship", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON>", "item.immersive_aircraft.engine": "Engine", "item.immersive_aircraft.sail": "Sail", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Enhan<PERSON>", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Steel Boiler", "item.immersive_aircraft.industrial_gears": "Industrial Gears", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroscope", "item.immersive_aircraft.hull_reinforcement": "Hull Reinforcement", "item.immersive_aircraft.improved_landing_gear": "Improved Landing Gear", "item.immersive_aircraft.rotary_cannon": "Rotary Cannon", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "Telescope", "item.immersive_aircraft.heavy_crossbow": "Heavy Crossbow", "item.immersive_aircraft.rotary_cannon.description": "A cannon of swift discharge, running with gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Drops TNT, doth not destroy blocks but deal heavy damage.", "item.immersive_aircraft.telescope.description": "A bulkier version of the spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "A heavy crossbow with a powerful punch, require arrows.", "item.immersive_aircraft.item.upgrade": "Aircraft Upgrade", "item.immersive_aircraft.item.weapon": "Aircraft Weapon", "item.immersive_aircraft.airship": "Airship", "item.immersive_aircraft.cargo_airship": "Cargo Airship", "item.immersive_aircraft.warship": "Warship", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Slow and fuel-hungry, yet doth bear an entire storage.", "item.immersive_aircraft.warship.description": "A flying fortress, slow yet heavily armed.", "item.immersive_aircraft.biplane.description": "This trusty, rustic biplane will take thee anywhere fast and rather reliably. Make sure thy runway is long enough.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Power, press on!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotor speed attained, ready for takeoff!", "immersive_aircraft.invalid_dimension": "This aircraft doth not work in this dimension.", "immersive_aircraft.out_of_ammo": "Out of ammo!", "immersive_aircraft.repair": "%s%% repaired!", "immersive_aircraft.tried_dismount": "Press again to leap out!", "immersive_aircraft.fuel.none": "No fuel!", "immersive_aircraft.fuel.out": "Thou hast run out of fuel!", "immersive_aircraft.fuel.low": "Thou art low on fuel!", "immersive_aircraft.fat.none": "No food!", "immersive_aircraft.fat.out": "Thou art too famished to fly!", "option.immersive_aircraft.general": "General Options", "option.immersive_aircraft.separateCamera": "Employ a separate camera whilst in aircraft.", "option.immersive_aircraft.useThirdPersonByDefault": "Defaults to third person visage within aircraft.", "option.immersive_aircraft.enableTrails": "Fancy steam trails.", "option.immersive_aircraft.enableAnimatedSails": "Wavy wavy Sails.", "option.immersive_aircraft.renderDistance": "Render distance measured in blocks.", "option.immersive_aircraft.fuelConsumption": "Fuel burn rate.", "option.immersive_aircraft.windClearWeather": "Base wind effect.", "option.immersive_aircraft.windRainWeather": "Wind with rainfall.", "option.immersive_aircraft.windThunderWeather": "Extra wind at thunderstorm.", "option.immersive_aircraft.repairSpeed": "Repair per click.", "option.immersive_aircraft.repairExhaustion": "Player exhaustion per click.", "option.immersive_aircraft.collisionDamage": "Collision damage.", "option.immersive_aircraft.burnFuelInCreative": "Burn fuel in creative mode.", "option.immersive_aircraft.acceptVanillaFuel": "Accept vanilla fuel.", "option.immersive_aircraft.useCustomKeybindSystem": "Employ multi-keybindings, may break certain mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Render the engine gauge over the hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Enable explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Enable destructive explosion.", "option.immersive_aircraft.enableCrashFire": "Enable fiery explosion.", "option.immersive_aircraft.crashExplosionRadius": "Size of crash explosion.", "option.immersive_aircraft.crashDamage": "<PERSON>h damage the player upon a crash.", "option.immersive_aircraft.preventKillThroughCrash": "Shall not kill the player upon a crash.", "option.immersive_aircraft.healthBarRow": "Offset the health bar of vehicles.", "option.immersive_aircraft.damagePerHealthPoint": "Higher values make aircraft more durable.", "option.immersive_aircraft.weaponsAreDestructive": "Permit some weapons to demolish blocks.", "option.immersive_aircraft.dropInventory": "Cast down the inventory, instead of saving it within the aircraft item.", "option.immersive_aircraft.dropUpgrades": "Cast down the upgrades and customization, instead of saving it within the aircraft item.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenerates health automatically every given tick, simulating vanilla behaviour.", "option.immersive_aircraft.requireShiftForRepair": "Repair only when holding shift, else merely enter the vehicle.", "immersive_aircraft.tooltip.no_target": "Needs must be assembled upon the floor!", "immersive_aircraft.tooltip.no_space": "Not enough space!", "immersive_aircraft.slot.booster": "Boost rockets", "immersive_aircraft.slot.weapon": "Weapon slot", "immersive_aircraft.slot.boiler": "Fuel slot", "immersive_aircraft.slot.banner": "Banner slot", "immersive_aircraft.slot.dye": "Dye slot", "immersive_aircraft.slot.upgrade": "Upgrade slot", "immersive_aircraft.upgrade.enginespeed": "%s%% engine power", "immersive_aircraft.upgrade.friction": "%s%% air friction", "immersive_aircraft.upgrade.acceleration": "%s%% takeoff speed", "immersive_aircraft.upgrade.durability": "%s%% durability", "immersive_aircraft.upgrade.fuel": "%s%% fuel requirement", "immersive_aircraft.upgrade.wind": "%s%% wind effect", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizing", "immersive_aircraft.tooltip.inventory": "Contains %s items."}