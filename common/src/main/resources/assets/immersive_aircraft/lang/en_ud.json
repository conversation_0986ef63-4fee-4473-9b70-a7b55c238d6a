{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "ʇɟɐɹɔɹᴉⱯ ǝʌᴉsɹǝɯɯI", "key.immersive_aircraft.multi_control_left": "ʇɟǝꞀ", "key.immersive_aircraft.multi_control_right": "ʇɥᵷᴉᴚ", "key.immersive_aircraft.multi_control_forward": "pɹɐʍɹoℲ", "key.immersive_aircraft.multi_control_backward": "pɹɐʍʞɔɐᗺ", "key.immersive_aircraft.multi_control_up": "d∩", "key.immersive_aircraft.multi_control_down": "uʍoᗡ", "key.immersive_aircraft.multi_control_pull": "ɹǝꞁꞁoɹʇuoƆ ꞁꞁnԀ", "key.immersive_aircraft.multi_control_push": "ɹǝꞁꞁoɹʇuoƆ ɥsnԀ", "key.immersive_aircraft.multi_use": "ʇunoɯ/uodɐǝʍ ǝs∩", "key.immersive_aircraft.fallback_control_left": "ʇɟǝꞀ", "key.immersive_aircraft.fallback_control_right": "ʇɥᵷᴉᴚ", "key.immersive_aircraft.fallback_control_forward": "pɹɐʍɹoℲ", "key.immersive_aircraft.fallback_control_backward": "pɹɐʍʞɔɐᗺ", "key.immersive_aircraft.fallback_control_up": "d∩", "key.immersive_aircraft.fallback_control_down": "uʍoᗡ", "key.immersive_aircraft.fallback_control_pull": "ɹǝꞁꞁoɹʇuoƆ ꞁꞁnԀ", "key.immersive_aircraft.fallback_control_push": "ɹǝꞁꞁoɹʇuoƆ ɥsnԀ", "key.immersive_aircraft.fallback_use": "ʇunoɯ/uodɐǝʍ ǝs∩", "key.immersive_aircraft.dismount": "ʇunoɯsᴉᗡ", "key.immersive_aircraft.boost": "ʇsooq ʇǝʞɔoᴚ", "entity.immersive_aircraft.airship": "dᴉɥsɹᴉⱯ", "entity.immersive_aircraft.cargo_airship": "dᴉɥsɹᴉⱯ oᵷɹɐƆ", "entity.immersive_aircraft.warship": "˙ʇᴉʃɹʍɐ", "entity.immersive_aircraft.biplane": "ǝuɐꞁdᴉᗺ", "entity.immersive_aircraft.gyrodyne": "ǝuʎpoɹʎ⅁", "entity.immersive_aircraft.quadrocopter": "ɹǝʇdoɔoɹpɐnꝹ", "item.immersive_aircraft.hull": "ꞁꞁnH", "item.immersive_aircraft.engine": "ǝuᴉᵷuƎ", "item.immersive_aircraft.sail": "ꞁᴉɐS", "item.immersive_aircraft.propeller": "ɹǝꞁꞁǝdoɹԀ", "item.immersive_aircraft.boiler": "ɹǝꞁᴉoᗺ", "item.immersive_aircraft.enhanced_propeller": "ɹǝꞁꞁǝdoɹԀ pǝɔuɐɥuƎ", "item.immersive_aircraft.eco_engine": "ǝuᴉᵷuƎ oɔƎ", "item.immersive_aircraft.nether_engine": "ǝuᴉᵷuƎ ɹǝɥʇǝN", "item.immersive_aircraft.steel_boiler": "ɹǝꞁᴉoᗺ ꞁǝǝʇS", "item.immersive_aircraft.industrial_gears": "sɹɐǝ⅁ ꞁɐᴉɹʇsnpuI", "item.immersive_aircraft.sturdy_pipes": "sǝdᴉԀ ʎpɹnʇS", "item.immersive_aircraft.gyroscope": "ǝdoɔsoɹʎ⅁", "item.immersive_aircraft.hull_reinforcement": "ʇuǝɯǝɔɹoɟuᴉǝᴚ ꞁꞁnH", "item.immersive_aircraft.improved_landing_gear": "ɹɐǝ⅁ ᵷuᴉpuɐꞀ pǝʌoɹdɯI", "item.immersive_aircraft.rotary_cannon": "uouuɐƆ ʎɹɐʇoᴚ", "item.immersive_aircraft.bomb_bay": "ʎɐᗺ qɯoᗺ", "item.immersive_aircraft.telescope": "ǝdoɔsǝꞁǝ⟘", "item.immersive_aircraft.heavy_crossbow": "ʍoqssoɹƆ ʎʌɐǝH", "item.immersive_aircraft.rotary_cannon.description": "˙ɹǝpʍodunᵷ ɥʇᴉʍ ᵷuᴉuunɹ uouuɐɔ ᵷuᴉɹᴉɟ-ʇsɐℲ", "item.immersive_aircraft.bomb_bay.description": "˙ǝᵷɐɯɐp ʎʌɐǝɥ sꞁɐǝp ʇnq sʞɔoꞁq ʎoɹʇsǝp ʇou sǝop '⟘N⟘ sdoɹᗡ", "item.immersive_aircraft.telescope.description": "˙ssɐꞁᵷʎds ǝɥʇ ɟo uoᴉsɹǝʌ ɹǝᴉʞꞁnq Ɐ", "item.immersive_aircraft.heavy_crossbow.description": "˙sʍoɹɹɐ sǝɹᴉnbǝɹ 'ɥɔund ꞁnɟɹǝʍod ɐ ɥʇᴉʍ ʍoqssoɹɔ ʎʌɐǝɥ Ɐ", "item.immersive_aircraft.item.upgrade": "ǝpɐɹᵷd∩ ʇɟɐɹɔɹᴉⱯ", "item.immersive_aircraft.item.weapon": "uodɐǝM ʇɟɐɹɔɹᴉⱯ", "item.immersive_aircraft.airship": "dᴉɥsɹᴉⱯ", "item.immersive_aircraft.cargo_airship": "dᴉɥsɹᴉⱯ oᵷɹɐƆ", "item.immersive_aircraft.warship": "˙ʇᴉʃɹʍɐ", "item.immersive_aircraft.biplane": "ǝuɐꞁdᴉᗺ", "item.immersive_aircraft.gyrodyne": "ǝuʎpoɹʎ⅁", "item.immersive_aircraft.quadrocopter": "ɹǝʇdoɔoɹpɐnꝹ", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "˙ǝᵷɐɹoʇs ǝɹᴉʇuǝ uɐ sǝᴉɹɹɐɔ ʇnq ʎɹᵷunɥ ꞁǝnɟ puɐ ʍoꞁS", "item.immersive_aircraft.warship.description": "˙sʇɐʍʇɐʍʍol ʍɐʍ, ʍɐl ʍuᴉl ʍoʍ, ʍorʍoɹʍ", "item.immersive_aircraft.biplane.description": "˙ɥᵷnouǝ ᵷuoꞁ sᴉ ʎɐʍunɹ ɹnoʎ ǝɹns ǝʞɐW ˙ǝꞁqɐᴉꞁǝɹ ɹǝɥʇɐɹ puɐ ʇsɐℲ", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "¡ᵷuᴉɥsnd dǝǝʞ 'ɹǝʍoԀ %%%d", "immersive_aircraft.gyrodyne_target_reached": "¡ɟɟoǝʞɐʇ ɹoɟ ʎpɐǝɹ 'pǝɥɔɐǝɹ pǝǝds ɹoʇoɹ ɯnɯᴉuᴉW", "immersive_aircraft.invalid_dimension": "˙uoᴉsuǝɯᴉp sᴉɥʇ uᴉ ʞɹoʍ ʇou sǝop ʇɟɐɹɔɹᴉɐ sᴉɥ⟘", "immersive_aircraft.out_of_ammo": "¡oɯɯɐ ɟo ʇnO", "immersive_aircraft.repair": "¡pǝɹᴉɐdǝɹ %%%s", "immersive_aircraft.tried_dismount": "¡ʇno dɯnɾ oʇ uᴉɐᵷɐ ssǝɹԀ", "immersive_aircraft.fuel.none": "¡ꞁǝnɟ oN", "immersive_aircraft.fuel.out": "¡ꞁǝnɟ ɟo ʇno uɐɹ no⅄", "immersive_aircraft.fuel.low": "¡ꞁǝnɟ uo ʍoꞁ ǝɹɐ no⅄", "immersive_aircraft.fat.none": "¡pooɟ oN", "immersive_aircraft.fat.out": "¡ʎꞁɟ oʇ ʎɹᵷunɥ ooʇ ǝɹɐ no⅄", "option.immersive_aircraft.general": "suoᴉʇdO ꞁɐɹǝuǝ⅁", "option.immersive_aircraft.separateCamera": "˙ʇɟɐɹɔɹᴉɐ uᴉ ɐɹǝɯɐɔ ǝʇɐɹɐdǝs ɐ ǝs∩", "option.immersive_aircraft.useThirdPersonByDefault": "˙ʇɟɐɹɔɹᴉɐ uᴉ ɐɹǝɯɐɔ uosɹǝd pɹᴉɥʇ oʇ sʇꞁnɐɟǝᗡ", "option.immersive_aircraft.enableTrails": "˙sꞁᴉɐɹʇ ɯɐǝʇs ʎɔuɐℲ", "option.immersive_aircraft.enableAnimatedSails": "˙sꞁᴉɐS ʎǝʌɐʍ ʎǝʌɐM", "option.immersive_aircraft.renderDistance": "˙sʞɔoꞁq uᴉ ǝɔuɐʇsᴉp ɹǝpuǝᴚ", "option.immersive_aircraft.fuelConsumption": "˙ǝʇɐɹ uɹnq ꞁǝnℲ", "option.immersive_aircraft.windClearWeather": "˙ʇɔǝɟɟǝ puᴉʍ ǝsɐᗺ", "option.immersive_aircraft.windRainWeather": "˙ꞁꞁɐɟuᴉɐɹ ʇɐ puᴉM", "option.immersive_aircraft.windThunderWeather": "˙ɹǝpunɥʇ ʇɐ puᴉʍ ɐɹʇxƎ", "option.immersive_aircraft.repairSpeed": "˙ʞɔᴉꞁɔ ɹǝd ɹᴉɐdǝᴚ", "option.immersive_aircraft.repairExhaustion": "˙sʍʇᴉxʍ ʍɥʍ sᴉ ʞlɐƃ {˙ouʍǝʍ 'ʎʍᴉ ɥʍƃ ʍuᴉ uᴉ ,ʍuʍp", "option.immersive_aircraft.collisionDamage": "˙ǝᵷɐɯɐp uoᴉsᴉꞁꞁoƆ", "option.immersive_aircraft.burnFuelInCreative": "˙ǝpoɯ ǝʌᴉʇɐǝɹɔ uᴉ ꞁǝnɟ uɹnᗺ", "option.immersive_aircraft.acceptVanillaFuel": "˙ꞁǝnɟ ɐꞁꞁᴉuɐʌ ʇdǝɔɔⱯ", "option.immersive_aircraft.useCustomKeybindSystem": "˙spoɯ uᴉɐʇɹǝɔ ʞɐǝɹq ʎɐɯ 'sᵷuᴉpuᴉqʎǝʞ-ᴉʇꞁnɯ ǝs∩", "option.immersive_aircraft.showHotbarEngineGauge": "ǝɹǝuɐɹ ǝɯuɐƃ ǝʍɐɹʇ ʍǝʍ sǝƃoʍ ʍoɹɹǝuɐ sᴉ ʍoƃ", "option.immersive_aircraft.enableCrashExplosion": "˙uoᴉsoꞁdxǝ ǝꞁqɐuƎ", "option.immersive_aircraft.enableCrashBlockDestruction": "˙uoᴉsoꞁdxǝ ǝʌᴉʇɔnɹʇsǝp ǝꞁqɐuƎ", "option.immersive_aircraft.enableCrashFire": "˙uoᴉsoꞁdxǝ ʎɹǝᴉɟ ǝꞁqɐuƎ", "option.immersive_aircraft.crashExplosionRadius": "˙uoᴉsoꞁdxǝ ɥsɐɹɔ ɟo ǝzᴉS", "option.immersive_aircraft.crashDamage": "˙ɥsɐɹɔ ɐ uo ɹǝʎɐꞁd ǝɥʇ ǝᵷɐɯɐᗡ", "option.immersive_aircraft.preventKillThroughCrash": "˙ɥsɐɹɔ ɐ uo ɹǝʎɐꞁd ǝɥʇ ꞁꞁᴉʞ ʇou ꞁꞁᴉM", "option.immersive_aircraft.healthBarRow": "˙sǝꞁɔᴉɥǝʌ ɟo ɹɐq ɥʇꞁɐǝɥ ǝɥʇ ʇǝsɟɟO", "option.immersive_aircraft.damagePerHealthPoint": "˙ǝꞁqɐɹnp ǝɹoɯ ʇɟɐɹɔɹᴉɐ ǝʞɐɯ sǝnꞁɐʌ ɹǝɥᵷᴉH", "option.immersive_aircraft.weaponsAreDestructive": "˙sʍǝuoɹp uoɯʍ uᴉ ʍɐʇʍ ʍɐʍ sᴉ ɹɐʍ pɐɹɥʍ ɐlq uɐl", "option.immersive_aircraft.dropInventory": "˙ʍlɥʍʍᴉu ʍᴉʇʇuᴉ sᴉ ʇɐʍʍol ʍop lɐsɹoʍ", "option.immersive_aircraft.dropUpgrades": "˙ʍɹʍɐ uoʍ lᴉʍ ʍolʍ ʇɐʍʍol ʍog ,ɥƃʍ sɐllʍoʍ olʍs ,ʍoʍ ʍɐʍ oʍ ʍlʍn", "option.immersive_aircraft.regenerateHealthEveryNTicks": "˙ʍɐǝʍ sʍʎʇuɯ sʇɐʍ ʍɐX uúʍsɐʇɐɹʍl lɯo ʍorp ʍɐʍ .ʍɹʍʇuoʇ lᴉA", "option.immersive_aircraft.requireShiftForRepair": "˙sʍuᴉl sʍǝaʍoʍ sʍაფA ʇʍuɐ ʍliɯ ɯↄ'ʍys", "immersive_aircraft.tooltip.no_target": "˙!ʍolʇ sɐʍɹʇs ʍolʍɔî ʇʍouʍ", "immersive_aircraft.tooltip.no_space": "˙!ǝʍɔʇ ʍouʎ,", "immersive_aircraft.slot.booster": "sʇǝʞɔoɹ ʇsooᗺ", "immersive_aircraft.slot.weapon": "ʇoꞁs uodɐǝM", "immersive_aircraft.slot.boiler": "ʇoꞁs ꞁǝnℲ", "immersive_aircraft.slot.banner": "ʇoꞁs ɹǝuuɐᗺ", "immersive_aircraft.slot.dye": "ʇoꞁs ǝʎᗡ", "immersive_aircraft.slot.upgrade": "ʇoꞁs ǝpɐɹᵷd∩", "immersive_aircraft.upgrade.enginespeed": "ɹǝʍod ǝuᴉᵷuǝ %%%s", "immersive_aircraft.upgrade.friction": "uoᴉʇɔᴉɹɟ ɹᴉɐ %%%s", "immersive_aircraft.upgrade.acceleration": "pǝǝds ɟɟoǝʞɐʇ %%%s", "immersive_aircraft.upgrade.durability": "ʎʇᴉꞁᴉqɐɹnp %%%s", "immersive_aircraft.upgrade.fuel": "ʇuǝɯǝɹᴉnbǝɹ ꞁǝnɟ %%%s", "immersive_aircraft.upgrade.wind": "ʇɔǝɟɟǝ puᴉʍ %%%s", "immersive_aircraft.upgrade.stabilizer": "gnizilabits %s%%", "immersive_aircraft.tooltip.inventory": "¹sʇᴉʍɹn sׁʍllᴉɐǝʍ Ǝ,pɐɹ˥ᴉʍ :sǝsǝʍʍɐʞ uʍɐʍ uʍ uᴉ pʍʇoʍ pɹᴉ ʎʇɐ ʞʍɐ sɐlışɐ"}