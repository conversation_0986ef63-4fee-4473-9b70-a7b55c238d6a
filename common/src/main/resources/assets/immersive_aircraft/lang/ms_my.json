{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Hadapan", "key.immersive_aircraft.multi_control_backward": "Belakang", "key.immersive_aircraft.multi_control_up": "Atas", "key.immersive_aircraft.multi_control_down": "<PERSON>wa<PERSON>", "key.immersive_aircraft.multi_control_pull": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_push": "<PERSON><PERSON>", "key.immersive_aircraft.multi_use": "Gunakan senjata/mount", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Hadapan", "key.immersive_aircraft.fallback_control_backward": "Belakang", "key.immersive_aircraft.fallback_control_up": "Atas", "key.immersive_aircraft.fallback_control_down": "<PERSON>wa<PERSON>", "key.immersive_aircraft.fallback_control_pull": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_push": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_use": "Gunakan senjata/mount", "key.immersive_aircraft.dismount": "<PERSON><PERSON>", "key.immersive_aircraft.boost": "Pendaratan roket", "entity.immersive_aircraft.airship": "<PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON>", "entity.immersive_aircraft.warship": "<PERSON><PERSON>", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "<PERSON><PERSON>", "entity.immersive_aircraft.quadrocopter": "Pesawat UAV", "item.immersive_aircraft.hull": "<PERSON><PERSON>", "item.immersive_aircraft.engine": "Enjin", "item.immersive_aircraft.sail": "<PERSON><PERSON>-<PERSON><PERSON>", "item.immersive_aircraft.propeller": "P<PERSON>utar", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.eco_engine": "Enjin Ekologi", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON>", "item.immersive_aircraft.steel_boiler": "<PERSON><PERSON>", "item.immersive_aircraft.industrial_gears": "Gear Industri", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Giroskop", "item.immersive_aircraft.hull_reinforcement": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.improved_landing_gear": "Gear Pendaratan Diperbaiki", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Busur Silang Berat", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON> be<PERSON> cepat yang berjalan dengan serbuk mesiu.", "item.immersive_aircraft.bomb_bay.description": "Menurunkan TNT, tidak menghancurkan blok tetapi menyebabkan kerosakan yang berat.", "item.immersive_aircraft.telescope.description": "Versi lebih besar dari teleskop.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>ur silang berat dengan daya tembak yang kuat, memerlukan anak panah.", "item.immersive_aircraft.item.upgrade": "Peningkatan Pesawat", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship": "<PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON>", "item.immersive_aircraft.warship": "<PERSON><PERSON>", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "<PERSON><PERSON>", "item.immersive_aircraft.quadrocopter": "Pesawat UAV", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lambat dan sangat lapar bahan bakar tetapi membawa sebuah simpanan penuh.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON><PERSON> terbang, perlahan tetapi sangat bersen<PERSON>ta.", "item.immersive_aircraft.biplane.description": "Cepat dan agak boleh dipercayai. Pastikan landasan anda cukup panjang.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Kuasa, teruskan tekanan!", "immersive_aircraft.gyrodyne_target_reached": "Kelajuan rotor minimum dicapai, sedia untuk berlepas!", "immersive_aircraft.invalid_dimension": "Pesawat terbang ini tidak berfungsi dalam dimensi ini.", "immersive_aircraft.out_of_ammo": "Tidak ada peluru!", "immersive_aircraft.repair": "%s%% diperbaiki!", "immersive_aircraft.tried_dismount": "Tekan lagi untuk keluar!", "immersive_aircraft.fuel.none": "T<PERSON>da bahan api!", "immersive_aircraft.fuel.out": "Anda habis bahan api!", "immersive_aircraft.fuel.low": "Anda rendah bahan api!", "immersive_aircraft.fat.none": "<PERSON><PERSON>da makanan!", "immersive_aircraft.fat.out": "Anda terlalu lapar untuk terbang!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON>", "option.immersive_aircraft.separateCamera": "<PERSON><PERSON><PERSON> kamera berbeza dalam pesawat.", "option.immersive_aircraft.useThirdPersonByDefault": "<PERSON><PERSON><PERSON>a menggunakan kamera orang ketiga dalam pesawat.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON> asap yang menarik.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON>-<PERSON><PERSON>.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> render dalam blok.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON> p<PERSON> bahan api.", "option.immersive_aircraft.windClearWeather": "<PERSON><PERSON><PERSON> angin asas.", "option.immersive_aircraft.windRainWeather": "<PERSON><PERSON> se<PERSON>a hujan.", "option.immersive_aircraft.windThunderWeather": "<PERSON><PERSON> tambahan semasa petir.", "option.immersive_aircraft.repairSpeed": "Perbaiki setiap klik.", "option.immersive_aircraft.repairExhaustion": "Kekurangan pemain per klik.", "option.immersive_aircraft.collisionDamage": "Kerosakan aki<PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "<PERSON><PERSON> bahan api dalam mod kreatif.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON> bahan api vanilla.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON>akan pengikat kunci pel<PERSON>ai, mungkin akan mematahkan beberapa mod.", "option.immersive_aircraft.showHotbarEngineGauge": "Menampilkan pengukur enjin di atas hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Akt<PERSON><PERSON> letupan.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktifkan letupan meros<PERSON>kan.", "option.immersive_aircraft.enableCrashFire": "Akt<PERSON><PERSON> letupan api.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON> letupan kemalangan.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON> pemain pada kemalangan.", "option.immersive_aircraft.preventKillThroughCrash": "Tidak akan membunuh pemain pada kemalangan.", "option.immersive_aircraft.healthBarRow": "<PERSON><PERSON><PERSON><PERSON> paparan kesihatan kender<PERSON>.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON> yang lebih tinggi membuat pesawat lebih tahan lasak.", "option.immersive_aircraft.weaponsAreDestructive": "Bolehkan beberapa senjata untuk menghancurkan blok.", "option.immersive_aircraft.dropInventory": "Tingg<PERSON>an inventori, se<PERSON><PERSON><PERSON> menyimp<PERSON>ya dalam item pesawat.", "option.immersive_aircraft.dropUpgrades": "Tinggalkan peningkatan dan pen<PERSON><PERSON>, se<PERSON><PERSON><PERSON> menyimp<PERSON>ya dalam item pesawat.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Secara automatik memulihkan kesihatan setiap tik yang diberikan, meniru per<PERSON>uan asal.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON> perbaiki apabila memegang shift, jika tidak, masuk ke kenderaan sahaja.", "immersive_aircraft.tooltip.no_target": "<PERSON><PERSON> dirakit di lantai!", "immersive_aircraft.tooltip.no_space": "Tidak cukup ruang!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON> pen<PERSON>bah", "immersive_aircraft.slot.weapon": "<PERSON>lot senjata", "immersive_aircraft.slot.boiler": "Slot bahan api", "immersive_aircraft.slot.banner": "<PERSON><PERSON> bendera", "immersive_aircraft.slot.dye": "Slot pewarna", "immersive_aircraft.slot.upgrade": "Slot peningkatan", "immersive_aircraft.upgrade.enginespeed": "%s%% kuasa enjin", "immersive_aircraft.upgrade.friction": "%s%% geseran udara", "immersive_aircraft.upgrade.acceleration": "%s%% kelajuan lepas landas", "immersive_aircraft.upgrade.durability": "%s%% ketahanan", "immersive_aircraft.upgrade.fuel": "%s%% keperluan bahan api", "immersive_aircraft.upgrade.wind": "%s%% kesan angin", "immersive_aircraft.upgrade.stabilizer": "%s%% menstabil", "immersive_aircraft.tooltip.inventory": "Mengandungi %s item."}