{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Velivoli immersivi", "key.immersive_aircraft.multi_control_left": "A sinistra", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "In avanti", "key.immersive_aircraft.multi_control_backward": "Indietro", "key.immersive_aircraft.multi_control_up": "Su", "key.immersive_aircraft.multi_control_down": "In basso", "key.immersive_aircraft.multi_control_pull": "Controllore di tiro", "key.immersive_aircraft.multi_control_push": "Controllore a spinta", "key.immersive_aircraft.multi_use": "Usa arma/montagna", "key.immersive_aircraft.fallback_control_left": "A sinistra", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "In avanti", "key.immersive_aircraft.fallback_control_backward": "Indietro", "key.immersive_aircraft.fallback_control_up": "Su", "key.immersive_aircraft.fallback_control_down": "In basso", "key.immersive_aircraft.fallback_control_pull": "Controllore di tiro", "key.immersive_aircraft.fallback_control_push": "Controllore a spinta", "key.immersive_aircraft.fallback_use": "Usa arma/montagna", "key.immersive_aircraft.dismount": "Smonta", "key.immersive_aircraft.boost": "Potenziamento del razzo", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON> da carico", "entity.immersive_aircraft.warship": "Nave da guerra", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocottero", "item.immersive_aircraft.hull": "Scafo", "item.immersive_aircraft.engine": "Motore", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "Elica", "item.immersive_aircraft.boiler": "Caldaia", "item.immersive_aircraft.enhanced_propeller": "Elica potenziata", "item.immersive_aircraft.eco_engine": "Motore Eco", "item.immersive_aircraft.nether_engine": "Motore di Nether", "item.immersive_aircraft.steel_boiler": "Caldaia in acciaio", "item.immersive_aircraft.industrial_gears": "Ingranaggi industriali", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Giroscopio", "item.immersive_aircraft.hull_reinforcement": "Rinforzo dello scafo", "item.immersive_aircraft.improved_landing_gear": "Carrello di atterraggio migliorato", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON> rotante", "item.immersive_aircraft.bomb_bay": "Baia delle bombe", "item.immersive_aircraft.telescope": "Telescopio", "item.immersive_aircraft.heavy_crossbow": "Balestra pesante", "item.immersive_aircraft.rotary_cannon.description": "Cannone a fuoco rapido alimentato con polvere da sparo.", "item.immersive_aircraft.bomb_bay.description": "Lascia cadere TNT, non distrugge i blocchi ma infligge danni ingenti.", "item.immersive_aircraft.telescope.description": "Una versione più ingombrante del cannocchiale.", "item.immersive_aircraft.heavy_crossbow.description": "Una balestra pesante con un colpo potente, che richiede frecce.", "item.immersive_aircraft.item.upgrade": "Aggiornamento dell'aeromobile", "item.immersive_aircraft.item.weapon": "Armamento aereo", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON> da carico", "item.immersive_aircraft.warship": "Nave da guerra", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocottero", "item.immersive_aircraft.airship.description": "I dirigibili non saranno i veicoli più veloci, ma di sicuro sono facili da manovrare.", "item.immersive_aircraft.cargo_airship.description": "Lenta e affamata di carburante, ma trasporta un intero magazzino.", "item.immersive_aircraft.warship.description": "Una fortezza volante, lenta ma pesantemente armata.", "item.immersive_aircraft.biplane.description": "Veloce e piuttosto affidabile. Assicurati che la tua pista sia abbastanza lunga.", "item.immersive_aircraft.gyrodyne.description": "Chi ha bisogno di un motore se si può alimentare l'aeromobile con la pura forza del corpo? Dagli una bella spinta e vola!", "item.immersive_aircraft.quadrocopter.description": "Un capolavoro di ingegneria! 4 rotori legati a del bambù. Perfetto per la costruzione, e questo è tutto.", "immersive_aircraft.gyrodyne_target": "%d%% Potenza, continua a spingere!", "immersive_aircraft.gyrodyne_target_reached": "Velocità minima del rotore raggiunta, pronto al decollo!", "immersive_aircraft.invalid_dimension": "Questo aereo non funziona in questa dimensione.", "immersive_aircraft.out_of_ammo": "Ho finito le munizioni!", "immersive_aircraft.repair": "%s%% riparato!", "immersive_aircraft.tried_dismount": "Premi di nuovo per saltare fuori!", "immersive_aircraft.fuel.none": "Niente carburante!", "immersive_aircraft.fuel.out": "Hai finito il carburante!", "immersive_aircraft.fuel.low": "Sei a corto di carburante!", "immersive_aircraft.fat.none": "Niente cibo!", "immersive_aircraft.fat.out": "Hai troppa fame per volare!", "option.immersive_aircraft.general": "Opzioni generali", "option.immersive_aircraft.separateCamera": "Usa una telecamera separata per gli aerei.", "option.immersive_aircraft.useThirdPersonByDefault": "La telecamera di default è in terza persona nell'aereo.", "option.immersive_aircraft.enableTrails": "Fantastici percorsi a vapore.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON>.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> di <PERSON> in blocchi.", "option.immersive_aircraft.fuelConsumption": "Tasso di combustione del carburante.", "option.immersive_aircraft.windClearWeather": "Effetto vento di base.", "option.immersive_aircraft.windRainWeather": "Vento alle precipitazioni.", "option.immersive_aircraft.windThunderWeather": "Vento extra al tuono.", "option.immersive_aircraft.repairSpeed": "Riparazione per clic.", "option.immersive_aircraft.repairExhaustion": "Esaurimento del giocatore per click.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "Brucia carburante in modalità creativa.", "option.immersive_aircraft.acceptVanillaFuel": "Accetta il carburante alla vaniglia.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON>a legami multipli, potrebbe rompere alcune mod.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderizza l'indicatore del motore sopra la hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Abilita l'esplosione.", "option.immersive_aircraft.enableCrashBlockDestruction": "Attiva l'esplosione distruttiva.", "option.immersive_aircraft.enableCrashFire": "Attiva l'esplosione di fuoco.", "option.immersive_aircraft.crashExplosionRadius": "Dimensione dell'esplosione dell'incidente.", "option.immersive_aircraft.crashDamage": "Danneggia il giocatore in caso di incidente.", "option.immersive_aircraft.preventKillThroughCrash": "Non ucciderà il giocatore in caso di incidente.", "option.immersive_aircraft.healthBarRow": "Compensa la barra della salute dei veicoli.", "option.immersive_aircraft.damagePerHealthPoint": "I valori più alti rendono gli aerei più resistenti.", "option.immersive_aircraft.weaponsAreDestructive": "Consenti ad alcune armi di distruggere i blocchi.", "option.immersive_aircraft.dropInventory": "Lascia l'inventario, invece di salvarlo all'interno dell'oggetto dell'aereo.", "option.immersive_aircraft.dropUpgrades": "Abbandona i potenziamenti e la personalizzazione, invece di salvarli all'interno dell'oggetto dell'aereo.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Rigenera automaticamente la salute a ogni tick, simulando il comportamento della versione vanilla.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON> solo se tieni il cambio, altrimenti entra nel veicolo.", "immersive_aircraft.tooltip.no_target": "Deve essere assemblato sul pavimento!", "immersive_aircraft.tooltip.no_space": "Non c'è abbastanza spazio!", "immersive_aircraft.slot.booster": "<PERSON><PERSON> di spinta", "immersive_aircraft.slot.weapon": "Slot per armi", "immersive_aircraft.slot.boiler": "Slot per il carburante", "immersive_aircraft.slot.banner": "Slot per banner", "immersive_aircraft.slot.dye": "Fessura di colorazione", "immersive_aircraft.slot.upgrade": "Slot di aggiornamento", "immersive_aircraft.upgrade.enginespeed": "%s%% di potenza del motore", "immersive_aircraft.upgrade.friction": "%s%% di attrito dell'aria", "immersive_aircraft.upgrade.acceleration": "%s%% di velocità di decollo", "immersive_aircraft.upgrade.durability": "%s%% durata", "immersive_aircraft.upgrade.fuel": "%s%% di carburante richiesto", "immersive_aircraft.upgrade.wind": "%s%% effetto vento", "immersive_aircraft.upgrade.stabilizer": "%s%% di stabilizzazione", "immersive_aircraft.tooltip.inventory": "Contiene %s ."}