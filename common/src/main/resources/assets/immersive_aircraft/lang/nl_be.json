{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersieve vliegtuigen", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Voorwaarts", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Omhoog", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Trekregelaar", "key.immersive_aircraft.multi_control_push": "Drukregelaar", "key.immersive_aircraft.multi_use": "Gebruik wapen/bergplaats", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Voorwaarts", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Omhoog", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Trekregelaar", "key.immersive_aircraft.fallback_control_push": "Drukregelaar", "key.immersive_aircraft.fallback_use": "Gebruik wapen/bergplaats", "key.immersive_aircraft.dismount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.boost": "Raketboost", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON>", "entity.immersive_aircraft.warship": "Oorlogsschip", "entity.immersive_aircraft.biplane": "Tweedekker", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Romp", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.boiler": "Ketel", "item.immersive_aircraft.enhanced_propeller": "Verbeterde schroef", "item.immersive_aircraft.eco_engine": "Eco-motor", "item.immersive_aircraft.nether_engine": "Ondergrondse motor", "item.immersive_aircraft.steel_boiler": "Stalen ketel", "item.immersive_aircraft.industrial_gears": "Industriël<PERSON>", "item.immersive_aircraft.sturdy_pipes": "Stevige buizen", "item.immersive_aircraft.gyroscope": "Gyroscoop", "item.immersive_aircraft.hull_reinforcement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.improved_landing_gear": "Verbeterd landingsgestel", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "Bommenru<PERSON>", "item.immersive_aircraft.telescope": "Telescoop", "item.immersive_aircraft.heavy_crossbow": "Zware kruisboog", "item.immersive_aircraft.rotary_cannon.description": "Snelvurend kanon op kruit.", "item.immersive_aircraft.bomb_bay.description": "Laat TNT vallen, vernietigt geen blokken maar richt zware schade aan.", "item.immersive_aircraft.telescope.description": "<PERSON><PERSON> grotere versie van de ve<PERSON>er.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>en z<PERSON> k<PERSON> met een krachtige stoot, vereist pijlen.", "item.immersive_aircraft.item.upgrade": "Vliegtuig Upgrade", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.warship": "Oorlogsschip", "item.immersive_aircraft.biplane": "Tweedekker", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Luchtschepen zijn miss<PERSON>en niet het snelste voertuig, maar ze zijn zeker gemakkelijk te manoeuvreren.", "item.immersive_aircraft.cargo_airship.description": "Traag en hongerig naar brands<PERSON>f, maar vervoert een hele opslag.", "item.immersive_aircraft.warship.description": "<PERSON>en vliegend fort, langzaam maar zwaar bewapend.", "item.immersive_aircraft.biplane.description": "Snel en tamelijk betrouwbaar. Zorg ervoor dat je <PERSON>baan lang genoeg is.", "item.immersive_aircraft.gyrodyne.description": "Wie heeft er een motor nodig als je het vliegtuig kunt a<PERSON><PERSON><PERSON><PERSON> met pure spierkracht? Geef het een flinke duw en het vliegt weg!", "item.immersive_aircraft.quadrocopter.description": "Een meesterwerk van techniek! 4 rotoren vastgebonden aan wat bamboe. Perfect om te bouwen, en dat is het wel zo'n beetje.", "immersive_aircraft.gyrodyne_target": "%d%% Kracht, doorzetten!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotors<PERSON><PERSON><PERSON> bere<PERSON>, klaar om op te stijgen!", "immersive_aircraft.invalid_dimension": "Dit vliegtuig werkt niet in deze dimensie.", "immersive_aircraft.out_of_ammo": "<PERSON>n munitie meer!", "immersive_aircraft.repair": "%s%% gerepareerd!", "immersive_aircraft.tried_dismount": "Druk nogmaals om eruit te springen!", "immersive_aircraft.fuel.none": "<PERSON>n brandstof!", "immersive_aircraft.fuel.out": "Je hebt geen brands<PERSON><PERSON> meer!", "immersive_aircraft.fuel.low": "Je hebt weinig brandstof!", "immersive_aircraft.fat.none": "Geen eten!", "immersive_aircraft.fat.out": "Je hebt te veel honger om te vliegen!", "option.immersive_aircraft.general": "Algemene opties", "option.immersive_aircraft.separateCamera": "Gebruik een aparte camera in vliegtuigen.", "option.immersive_aircraft.useThirdPersonByDefault": "<PERSON><PERSON><PERSON> standaard op derde persoon camera in vliegtuig.", "option.immersive_aircraft.enableTrails": "Zin in stoomsporen.", "option.immersive_aircraft.enableAnimatedSails": "Golvende golvende zeilen.", "option.immersive_aircraft.renderDistance": "Render afstand in blokken.", "option.immersive_aircraft.fuelConsumption": "Brandstofverbruik.", "option.immersive_aircraft.windClearWeather": "Basiswindeffect.", "option.immersive_aircraft.windRainWeather": "Wind bij neerslag.", "option.immersive_aircraft.windThunderWeather": "Extra wind bij onweer.", "option.immersive_aircraft.repairSpeed": "Reparatie per klik.", "option.immersive_aircraft.repairExhaustion": "Spelers vermoeidheid per klik.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON>e door aanri<PERSON>ding.", "option.immersive_aircraft.burnFuelInCreative": "Verbrand brandstof in de creatieve modus.", "option.immersive_aircraft.acceptVanillaFuel": "Accepteer <PERSON><PERSON>brand<PERSON><PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "Gebruik multi-keybindings, kan bepaalde mods breken.", "option.immersive_aircraft.showHotbarEngineGauge": "<PERSON><PERSON> de motormeter weer boven de hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Explosie inschakelen.", "option.immersive_aircraft.enableCrashBlockDestruction": "Destructieve explosie inschakelen.", "option.immersive_aircraft.enableCrashFire": "Vurige explosie mogelijk maken.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON><PERSON> de explos<PERSON>.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON><PERSON><PERSON> de s<PERSON>er bij een crash.", "option.immersive_aircraft.preventKillThroughCrash": "<PERSON><PERSON> de speler niet bij een crash.", "option.immersive_aircraft.healthBarRow": "Compense<PERSON> de gezondheidsbalk van v<PERSON>rtuigen.", "option.immersive_aircraft.damagePerHealthPoint": "Hogere waarden maken vliegtuigen duurzamer.", "option.immersive_aircraft.weaponsAreDestructive": "Sta toe dat sommige wapens blokken vernietigen.", "option.immersive_aircraft.dropInventory": "Laat de inventaris vallen, in plaats van hem op te slaan in het vliegtuigitem.", "option.immersive_aircraft.dropUpgrades": "Laat de upgrades en aanpassingen vallen, in plaats van ze op te slaan in het vliegtuigitem.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneert automatisch gezondheid elke gegeven tick, simuleert de vanilla gedrag.", "option.immersive_aircraft.requireShiftForRepair": "Repareer alleen wanneer je shift ingedrukt houdt, anders stap gewoon in het voertuig.", "immersive_aircraft.tooltip.no_target": "Moet op de vloer in elkaar worden gezet!", "immersive_aircraft.tooltip.no_space": "<PERSON>et geno<PERSON> rui<PERSON>!", "immersive_aircraft.slot.booster": "Aanjaagraketten", "immersive_aircraft.slot.weapon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.boiler": "Brandstofsleuf", "immersive_aircraft.slot.banner": "Banner slot", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Upgrade slot", "immersive_aircraft.upgrade.enginespeed": "%s%% motorvermogen", "immersive_aircraft.upgrade.friction": "%s%% luchtwrijving", "immersive_aircraft.upgrade.acceleration": "%s%% startsnelheid", "immersive_aircraft.upgrade.durability": "%s%% duurzaamheid", "immersive_aircraft.upgrade.fuel": "%s%% brandstofbehoefte", "immersive_aircraft.upgrade.wind": "%s%% windeffect", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliseren", "immersive_aircraft.tooltip.inventory": "Bevat %s items."}