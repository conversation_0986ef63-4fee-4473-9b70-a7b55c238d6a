{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aeroplano Immersivo", "key.immersive_aircraft.multi_control_left": "Sinistra", "key.immersive_aircraft.multi_control_right": "Destra", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Indietro", "key.immersive_aircraft.multi_control_up": "Su", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Tira Controller", "key.immersive_aircraft.multi_control_push": "Spingi Controller", "key.immersive_aircraft.multi_use": "Usa arma/monte", "key.immersive_aircraft.fallback_control_left": "Sinistra", "key.immersive_aircraft.fallback_control_right": "Destra", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Indietro", "key.immersive_aircraft.fallback_control_up": "Su", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Tira Controller", "key.immersive_aircraft.fallback_control_push": "Spingi Controller", "key.immersive_aircraft.fallback_use": "Usa arma/monte", "key.immersive_aircraft.dismount": "Smounta", "key.immersive_aircraft.boost": "<PERSON><PERSON> razzo", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "Venezia Cargo Airship", "entity.immersive_aircraft.warship": "Nave da guerra", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull": "Scafo", "item.immersive_aircraft.engine": "Motore", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "<PERSON><PERSON>", "item.immersive_aircraft.boiler": "Caldaia", "item.immersive_aircraft.enhanced_propeller": "Elica migliorata", "item.immersive_aircraft.eco_engine": "Motore Eco", "item.immersive_aircraft.nether_engine": "Motore del Nether", "item.immersive_aircraft.steel_boiler": "Caldaia in acciaio", "item.immersive_aircraft.industrial_gears": "Ingranaggi industriali", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Giroscopio", "item.immersive_aircraft.hull_reinforcement": "Rinforzo dello scafo", "item.immersive_aircraft.improved_landing_gear": "Migliorata Trattura", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "<PERSON><PERSON>", "item.immersive_aircraft.telescope": "Telescopio", "item.immersive_aircraft.heavy_crossbow": "Balestra Pesante", "item.immersive_aircraft.rotary_cannon.description": "Cannoncino a fuoco veloce funzionante a polvere da sparo.", "item.immersive_aircraft.bomb_bay.description": "Rilascia TNT, non distrugge i blocchi ma infligge pesanti danni.", "item.immersive_aircraft.telescope.description": "Una versione più ingombrante del cannocchiale.", "item.immersive_aircraft.heavy_crossbow.description": "Una balestra pesante con un colpo potente, richiede frecce.", "item.immersive_aircraft.item.upgrade": "Aggiornamento Aeromobile", "item.immersive_aircraft.item.weapon": "Arma Aerea", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "Venezia Cargo Airship", "item.immersive_aircraft.warship": "Nave da guerra", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lento e che consuma tanto carburante ma porta un'intera scorta.", "item.immersive_aircraft.warship.description": "Una fortezza volante, lenta ma pesantemente armata.", "item.immersive_aircraft.biplane.description": "Veloce e piuttosto affidabile. Assicurati che la tua pista sia sufficientemente lunga.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Potenza, continua a spingere!", "immersive_aircraft.gyrodyne_target_reached": "Velocità minima del rotore raggiunta, pronta per il decollo!", "immersive_aircraft.invalid_dimension": "Questo aereo non funziona in questa dimensione.", "immersive_aircraft.out_of_ammo": "Esaurito il munizionamento!", "immersive_aircraft.repair": "%s%% riparato!", "immersive_aircraft.tried_dismount": "Premi di nuovo per saltare giù!", "immersive_aircraft.fuel.none": "N<PERSON>un carburante!", "immersive_aircraft.fuel.out": "Hai finito il carburante!", "immersive_aircraft.fuel.low": "Sei a corto di carburante!", "immersive_aircraft.fat.none": "<PERSON><PERSON>un cibo!", "immersive_aircraft.fat.out": "Sei troppo affamato per volare!", "option.immersive_aircraft.general": "Opzioni Generali", "option.immersive_aircraft.separateCamera": "Usa una camera separata nell'aeromobile.", "option.immersive_aircraft.useThirdPersonByDefault": "Di default si usa la camera in terza persona nell'aeromobile.", "option.immersive_aircraft.enableTrails": "Sentieri di vapore eleganti.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON>.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> di <PERSON> in blocchi.", "option.immersive_aircraft.fuelConsumption": "Tasso di consumo carburante.", "option.immersive_aircraft.windClearWeather": "Effetto del vento base.", "option.immersive_aircraft.windRainWeather": "Vento durante la pioggia.", "option.immersive_aircraft.windThunderWeather": "Vento extra durante i tuoni.", "option.immersive_aircraft.repairSpeed": "Riparazione per clic.", "option.immersive_aircraft.repairExhaustion": "Esaurimento del giocatore per clic.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON> da <PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "Bruciare carburante in modalità creativa.", "option.immersive_aircraft.acceptVanillaFuel": "Accetta carburante vaniglia.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON><PERSON> le multi-accensioni, potrebbe rompere alcuni mod.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderizza il misuratore del motore sopra la barra degli hotkey.", "option.immersive_aircraft.enableCrashExplosion": "Abilita esplosione.", "option.immersive_aircraft.enableCrashBlockDestruction": "Abilita esplosione distruttiva.", "option.immersive_aircraft.enableCrashFire": "Abilita esplosione infuocata.", "option.immersive_aircraft.crashExplosionRadius": "Dimensione dell'esplosione in caso di incidente.", "option.immersive_aircraft.crashDamage": "Danneggia il giocatore in caso di incidente.", "option.immersive_aircraft.preventKillThroughCrash": "Non ucciderà il giocatore in caso di incidente.", "option.immersive_aircraft.healthBarRow": "Compensa la barra della salute dei veicoli.", "option.immersive_aircraft.damagePerHealthPoint": "Valori più alti rendono gli aerei più resistenti.", "option.immersive_aircraft.weaponsAreDestructive": "Permeti a qualche arma de distrugere i blocchi.", "option.immersive_aircraft.dropInventory": "Lassa cadere l'inventario, invece de salvarlo dentro l'oggetto aereo.", "option.immersive_aircraft.dropUpgrades": "Lassa cadere i miglioramenti e le personalizzazioni, invece de salvarli dentro l'oggetto aereo.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Rigenera automaticamente la salute ogni tick dato, simulando il comportamento vanilla.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON> solo quando tieni premuto il maiuscolo, altrimenti entra solo nel veicolo.", "immersive_aircraft.tooltip.no_target": "Bisogna montarlo sul pavimento!", "immersive_aircraft.tooltip.no_space": "Non gh'è massa spazi!", "immersive_aircraft.slot.booster": "<PERSON><PERSON> di spinta", "immersive_aircraft.slot.weapon": "Slot per arma", "immersive_aircraft.slot.boiler": "Slot carburante", "immersive_aircraft.slot.banner": "Slot bandiera", "immersive_aircraft.slot.dye": "Slot colorante", "immersive_aircraft.slot.upgrade": "Slot aggiornamento", "immersive_aircraft.upgrade.enginespeed": "%s%% potenza del motore", "immersive_aircraft.upgrade.friction": "%s%% attrito dell'aria", "immersive_aircraft.upgrade.acceleration": "%s%% velocità di decollo", "immersive_aircraft.upgrade.durability": "%s%% durabilità", "immersive_aircraft.upgrade.fuel": "%s%% richiesta di carburante", "immersive_aircraft.upgrade.wind": "%s%% effetto del vento", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizzante", "immersive_aircraft.tooltip.inventory": "Contien %s og<PERSON><PERSON>."}