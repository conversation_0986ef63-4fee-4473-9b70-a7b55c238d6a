{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aereoplans <PERSON>", "key.immersive_aircraft.multi_control_left": "Sinistra", "key.immersive_aircraft.multi_control_right": "Destra", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Indietro", "key.immersive_aircraft.multi_control_up": "Su", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Tira il controller", "key.immersive_aircraft.multi_control_push": "Spingi il controller", "key.immersive_aircraft.multi_use": "Utilize arma/mont", "key.immersive_aircraft.fallback_control_left": "Sinistra", "key.immersive_aircraft.fallback_control_right": "Destra", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Indietro", "key.immersive_aircraft.fallback_control_up": "Su", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Tira il controller", "key.immersive_aircraft.fallback_control_push": "Spingi il controller", "key.immersive_aircraft.fallback_use": "Utilize arma/mont", "key.immersive_aircraft.dismount": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.boost": "<PERSON><PERSON> razzo", "entity.immersive_aircraft.airship": "Dirigent", "entity.immersive_aircraft.cargo_airship": "Aeromobile da carico", "entity.immersive_aircraft.warship": "Nave da guerra", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodino", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull": "Scaf", "item.immersive_aircraft.engine": "Motore", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Eliche", "item.immersive_aircraft.boiler": "Caldaia", "item.immersive_aircraft.enhanced_propeller": "Elica migliorata", "item.immersive_aircraft.eco_engine": "Motore Eco", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON>", "item.immersive_aircraft.steel_boiler": "Caldaia d'acciaio", "item.immersive_aircraft.industrial_gears": "Ingranaggi industriali", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Giroscopio", "item.immersive_aircraft.hull_reinforcement": "Rafforzamento dello scafo", "item.immersive_aircraft.improved_landing_gear": "Carrello di atterraggio migliorato", "item.immersive_aircraft.rotary_cannon": "Cannon Rotative", "item.immersive_aircraft.bomb_bay": "Bomba Cjave", "item.immersive_aircraft.telescope": "Telescopi", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON>ce pesante", "item.immersive_aircraft.rotary_cannon.description": "Cannon che spara svelt cunt gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Cade TNT, non distrûs i bloccs ma al fa pesant danni.", "item.immersive_aircraft.telescope.description": "Une version plui ingrobade dal spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "Une croce pesante cun un colp puissâ, al richiedi frec.", "item.immersive_aircraft.item.upgrade": "Aggiornamento aeromobile", "item.immersive_aircraft.item.weapon": "Arma di aviôn", "item.immersive_aircraft.airship": "Dirigent", "item.immersive_aircraft.cargo_airship": "Aeromobile da carico", "item.immersive_aircraft.warship": "Nave da guerra", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodino", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lent e ciui a combustibil ma trasporti une intere scorte.", "item.immersive_aircraft.warship.description": "Une fortezze volante, lenta ma ben armade.", "item.immersive_aircraft.biplane.description": "Svelc e stâ ben affidabil. Assicurati che il to pista sia une lunghe abbastanza.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Potenza, continua a spingere!", "immersive_aircraft.gyrodyne_target_reached": "Velocità minima del rotore raggiunta, pronto per il decollo!", "immersive_aircraft.invalid_dimension": "Chest aviôn no al funziona in chest dimension.", "immersive_aircraft.out_of_ammo": "Fûr di munizion!", "immersive_aircraft.repair": "%s%% reparât!", "immersive_aircraft.tried_dismount": "Premi ancje par saltâ fûr!", "immersive_aircraft.fuel.none": "N<PERSON>un carburante!", "immersive_aircraft.fuel.out": "Hai finito il carburante!", "immersive_aircraft.fuel.low": "Hai poco carburante!", "immersive_aircraft.fat.none": "<PERSON><PERSON>un cibo!", "immersive_aircraft.fat.out": "Sei troppo affamato per volare!", "option.immersive_aircraft.general": "Opzioni generali", "option.immersive_aircraft.separateCamera": "Usa una telecamera separata nell'aeromobile.", "option.immersive_aircraft.useThirdPersonByDefault": "Di default fotocamera in terza persona nell'aeromobile.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON> di vapore elaborate.", "option.immersive_aircraft.enableAnimatedSails": "Vele ondulate.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> di <PERSON> in blocchi.", "option.immersive_aircraft.fuelConsumption": "Tasso di consumo carburante.", "option.immersive_aircraft.windClearWeather": "Effetto vento di base.", "option.immersive_aircraft.windRainWeather": "Vento in caso di pioggia.", "option.immersive_aircraft.windThunderWeather": "Vento extra in caso di temporale.", "option.immersive_aircraft.repairSpeed": "Ripara par clic.", "option.immersive_aircraft.repairExhaustion": "Esguiz di jessà al giucador par clic.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON> da <PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "Brucia carburante in modalità creativa.", "option.immersive_aircraft.acceptVanillaFuel": "Accetta carburante alla vaniglia.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON>a chiavi multiple, potrebbe rompere alcuni mod.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderize il manometro dal motore su la barra di strumenti.", "option.immersive_aircraft.enableCrashExplosion": "Abilitâ esplosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Abilitâ esplosion destructiva.", "option.immersive_aircraft.enableCrashFire": "Abilitâ esplosion incandescente.", "option.immersive_aircraft.crashExplosionRadius": "Dimension dal esplosion dal schianto.", "option.immersive_aircraft.crashDamage": "Dannegi il giucadôr in un schianto.", "option.immersive_aircraft.preventKillThroughCrash": "Non al morirà il giucadôr in un schianto.", "option.immersive_aircraft.healthBarRow": "Offsetâ la barriere di salute dai velic.", "option.immersive_aircraft.damagePerHealthPoint": "Valôrs plui alti rendin i aeri plui resistents.", "option.immersive_aircraft.weaponsAreDestructive": "Permet di destrui bloccs cun certi armaments.", "option.immersive_aircraft.dropInventory": "Scambia il inventari, invece di salvâlu dentri al element aéronautic.", "option.immersive_aircraft.dropUpgrades": "Scambia i potenziaments e le personalizzazions, invece di salvâli dentri al element aéronautic.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneri automaticament la salût ogni determinate tick, simuland il comportament vanil.", "option.immersive_aircraft.requireShiftForRepair": "A ripara dome cu l'shifti, altrimenti entri dome nel veicul.", "immersive_aircraft.tooltip.no_target": "A è da assemblâ sul paviment!", "immersive_aircraft.tooltip.no_space": "No è enough spazi!", "immersive_aircraft.slot.booster": "<PERSON><PERSON> di impulsione", "immersive_aircraft.slot.weapon": "Slot di arma", "immersive_aircraft.slot.boiler": "Slot carburante", "immersive_aircraft.slot.banner": "Slot banner", "immersive_aircraft.slot.dye": "Slot colorante", "immersive_aircraft.slot.upgrade": "Slot di aggiornamento", "immersive_aircraft.upgrade.enginespeed": "%s%% potenza del motore", "immersive_aircraft.upgrade.friction": "%s%% attrito dell'aria", "immersive_aircraft.upgrade.acceleration": "%s%% velocità di decollo", "immersive_aircraft.upgrade.durability": "%s%% durabilità", "immersive_aircraft.upgrade.fuel": "%s%% richiesta di carburante", "immersive_aircraft.upgrade.wind": "%s%% effetto vento", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizand", "immersive_aircraft.tooltip.inventory": "Conten %s items."}