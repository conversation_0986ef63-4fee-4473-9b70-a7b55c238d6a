{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aeroplani Immersivi", "key.immersive_aircraft.multi_control_left": "Sinistra", "key.immersive_aircraft.multi_control_right": "Destra", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "Indietro", "key.immersive_aircraft.multi_control_up": "Su", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Controllo di trazione", "key.immersive_aircraft.multi_control_push": "Controllo di spinta", "key.immersive_aircraft.multi_use": "Usa arma/montaggio", "key.immersive_aircraft.fallback_control_left": "Sinistra", "key.immersive_aircraft.fallback_control_right": "Destra", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "Indietro", "key.immersive_aircraft.fallback_control_up": "Su", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Controllo di trazione", "key.immersive_aircraft.fallback_control_push": "Controllo di spinta", "key.immersive_aircraft.fallback_use": "Usa arma/montaggio", "key.immersive_aircraft.dismount": "Scend<PERSON>", "key.immersive_aircraft.boost": "Razzo di spinta", "entity.immersive_aircraft.airship": "Aeromobile", "entity.immersive_aircraft.cargo_airship": "Cargo Aerodrom", "entity.immersive_aircraft.warship": "Nave de guerra", "entity.immersive_aircraft.biplane": "Biplano", "entity.immersive_aircraft.gyrodyne": "Gyrodyna", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull": "Scafo", "item.immersive_aircraft.engine": "Motore", "item.immersive_aircraft.sail": "Vela", "item.immersive_aircraft.propeller": "Eliche", "item.immersive_aircraft.boiler": "Caldaia", "item.immersive_aircraft.enhanced_propeller": "Elica migliorata", "item.immersive_aircraft.eco_engine": "Motore ecologico", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON>", "item.immersive_aircraft.steel_boiler": "Caldaia in acciaio", "item.immersive_aircraft.industrial_gears": "Ingranaggi industriali", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Giroscopio", "item.immersive_aircraft.hull_reinforcement": "Rinforzo dello scafo", "item.immersive_aircraft.improved_landing_gear": "Telaio <PERSON>", "item.immersive_aircraft.rotary_cannon": "<PERSON>", "item.immersive_aircraft.bomb_bay": "Bombardamento", "item.immersive_aircraft.telescope": "Telescopio", "item.immersive_aircraft.heavy_crossbow": "Arco Cross Heavy", "item.immersive_aircraft.rotary_cannon.description": "Cannon a fuoco veloce che funziona con polvere da sparo.", "item.immersive_aircraft.bomb_bay.description": "Lancia TNT, non distrugge blocchi ma infligge danni pesanti.", "item.immersive_aircraft.telescope.description": "Una versione più robusta del cannocchiale.", "item.immersive_aircraft.heavy_crossbow.description": "Un arco cross heavy con un potente colpo, richiede frecce.", "item.immersive_aircraft.item.upgrade": "Aggiornamento aeromobile", "item.immersive_aircraft.item.weapon": "Arma Aeronautica", "item.immersive_aircraft.airship": "Aeromobile", "item.immersive_aircraft.cargo_airship": "Cargo Aerodrom", "item.immersive_aircraft.warship": "Nave de guerra", "item.immersive_aircraft.biplane": "Biplano", "item.immersive_aircraft.gyrodyne": "Gyrodyna", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lent e consuma carburant ma porta on stoccaggio intero.", "item.immersive_aircraft.warship.description": "Una fortezza volante, lenta ma pesantemente armata.", "item.immersive_aircraft.biplane.description": "Veloce e piuttosto affidabile. Assicurati che la tua pista sia abbastanza lunga.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Potenza, continua così!", "immersive_aircraft.gyrodyne_target_reached": "Velocità minima del rotore raggiunta, pronta per il decollo!", "immersive_aircraft.invalid_dimension": "Quest aeroplano non funziona in questa dimensione.", "immersive_aircraft.out_of_ammo": "Senza munizioni!", "immersive_aircraft.repair": "%s%% riparato!", "immersive_aircraft.tried_dismount": "Premi di nuovo per saltare!", "immersive_aircraft.fuel.none": "N<PERSON>un carburante!", "immersive_aircraft.fuel.out": "Hai finito il carburante!", "immersive_aircraft.fuel.low": "Sei a corto di carburante!", "immersive_aircraft.fat.none": "<PERSON><PERSON>un cibo!", "immersive_aircraft.fat.out": "Sei troppo affamato per volare!", "option.immersive_aircraft.general": "Opzioni generali", "option.immersive_aircraft.separateCamera": "Usa una camera separata nell'aeromobile.", "option.immersive_aircraft.useThirdPersonByDefault": "Imposta sulla camera in terza persona di default nell'aeromobile.", "option.immersive_aircraft.enableTrails": "Scie di vapore elegantemente.", "option.immersive_aircraft.enableAnimatedSails": "Vele ondulate.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> di <PERSON> in blocchi.", "option.immersive_aircraft.fuelConsumption": "Tasso di consumo carburante.", "option.immersive_aircraft.windClearWeather": "Effetto vento di base.", "option.immersive_aircraft.windRainWeather": "Vento durante la pioggia.", "option.immersive_aircraft.windThunderWeather": "Vento extra durante il temporale.", "option.immersive_aircraft.repairSpeed": "R<PERSON>ara per click.", "option.immersive_aircraft.repairExhaustion": "Stanchezza del giocatore per clic.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON> da <PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "Brucia carburante in modalità creativa.", "option.immersive_aircraft.acceptVanillaFuel": "Accetta carburante vanilla.", "option.immersive_aircraft.useCustomKeybindSystem": "Usa combinazioni di tasti multipli, potrebbe rompere alcuni mod.", "option.immersive_aircraft.showHotbarEngineGauge": "Mostra il manometro del motore sopra la barra di hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Abilita esplosione.", "option.immersive_aircraft.enableCrashBlockDestruction": "Abilita esplosione distruttiva.", "option.immersive_aircraft.enableCrashFire": "Abilita esplosione infuocata.", "option.immersive_aircraft.crashExplosionRadius": "Dimensione dell'esplosione da crash.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON> dann da crash al giocatore.", "option.immersive_aircraft.preventKillThroughCrash": "No mètt a mort el giocatore in case de crash.", "option.immersive_aircraft.healthBarRow": "Spostare la barra della salute dei veicoli.", "option.immersive_aircraft.damagePerHealthPoint": "Valori più alti rendono l'aeroplano più resistente.", "option.immersive_aircraft.weaponsAreDestructive": "Permet de distrugé i bloc.", "option.immersive_aircraft.dropInventory": "Fia calà l'inventari, in vez de salvàll dent de l'oggetto aere.", "option.immersive_aircraft.dropUpgrades": "Fia calà i miglioramenti e la personalizzazione, in vez de salvàll dent de l'oggetto aere.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenera automaticamente la salute ogni tick dat, simulando il comportamento di vanilla.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON>aira solo se te tieni shift, sennò entra sol semplicemente nel veicolo.", "immersive_aircraft.tooltip.no_target": "Gha da vess assemblà sü la tera!", "immersive_aircraft.tooltip.no_space": "Mica abastanza spazi!", "immersive_aircraft.slot.booster": "<PERSON><PERSON> di spinta", "immersive_aircraft.slot.weapon": "Slot per arma", "immersive_aircraft.slot.boiler": "Slot carburante", "immersive_aircraft.slot.banner": "Slot banner", "immersive_aircraft.slot.dye": "Slot tintura", "immersive_aircraft.slot.upgrade": "Slot upgrade", "immersive_aircraft.upgrade.enginespeed": "%s%% potenza del motore", "immersive_aircraft.upgrade.friction": "%s%% attrito dell'aria", "immersive_aircraft.upgrade.acceleration": "%s%% velocità di decollo", "immersive_aircraft.upgrade.durability": "%s%% durata", "immersive_aircraft.upgrade.fuel": "%s%% richiesta di carburante", "immersive_aircraft.upgrade.wind": "%s%% effetto del vento", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizzando", "immersive_aircraft.tooltip.inventory": "Conten %s og<PERSON>ti."}