{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "몰입형 항공기", "key.immersive_aircraft.multi_control_left": "왼쪽", "key.immersive_aircraft.multi_control_right": "오른쪽", "key.immersive_aircraft.multi_control_forward": "앞으로", "key.immersive_aircraft.multi_control_backward": "뒤로", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "아래로", "key.immersive_aircraft.multi_control_pull": "풀 컨트롤러", "key.immersive_aircraft.multi_control_push": "푸시 컨트롤러", "key.immersive_aircraft.multi_use": "무기/탈것 사용", "key.immersive_aircraft.fallback_control_left": "왼쪽", "key.immersive_aircraft.fallback_control_right": "오른쪽", "key.immersive_aircraft.fallback_control_forward": "앞으로", "key.immersive_aircraft.fallback_control_backward": "뒤로", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "아래로", "key.immersive_aircraft.fallback_control_pull": "풀 컨트롤러", "key.immersive_aircraft.fallback_control_push": "푸시 컨트롤러", "key.immersive_aircraft.fallback_use": "무기/탈것 사용", "key.immersive_aircraft.dismount": "분리", "key.immersive_aircraft.boost": "로켓 부스트", "entity.immersive_aircraft.airship": "비행선", "entity.immersive_aircraft.cargo_airship": "화물 비행선", "entity.immersive_aircraft.warship": "워쉽", "entity.immersive_aircraft.biplane": "복엽기", "entity.immersive_aircraft.gyrodyne": "자이로다인", "entity.immersive_aircraft.quadrocopter": "쿼드로콥터", "item.immersive_aircraft.hull": "선체", "item.immersive_aircraft.engine": "엔진", "item.immersive_aircraft.sail": "항해", "item.immersive_aircraft.propeller": "프로펠러", "item.immersive_aircraft.boiler": "보일러", "item.immersive_aircraft.enhanced_propeller": "향상된 프로펠러", "item.immersive_aircraft.eco_engine": "친환경 엔진", "item.immersive_aircraft.nether_engine": "네더 엔진", "item.immersive_aircraft.steel_boiler": "강철 보일러", "item.immersive_aircraft.industrial_gears": "산업용 기어", "item.immersive_aircraft.sturdy_pipes": "견고한 파이프", "item.immersive_aircraft.gyroscope": "자이로스코프", "item.immersive_aircraft.hull_reinforcement": "선체 강화", "item.immersive_aircraft.improved_landing_gear": "향상된 랜딩 기어", "item.immersive_aircraft.rotary_cannon": "로터리 캐논", "item.immersive_aircraft.bomb_bay": "폭탄 만", "item.immersive_aircraft.telescope": "망원경", "item.immersive_aircraft.heavy_crossbow": "무거운 석궁", "item.immersive_aircraft.rotary_cannon.description": "화약으로 작동하는 고속 발사 대포.", "item.immersive_aircraft.bomb_bay.description": "TNT를 떨어뜨리고 블록을 파괴하지는 않지만 큰 피해를 줍니다.", "item.immersive_aircraft.telescope.description": "망원경의 부피가 커진 버전입니다.", "item.immersive_aircraft.heavy_crossbow.description": "강력한 펀치를 가진 무거운 석궁에는 화살이 필요합니다.", "item.immersive_aircraft.item.upgrade": "항공기 업그레이드", "item.immersive_aircraft.item.weapon": "항공기 무기", "item.immersive_aircraft.airship": "비행선", "item.immersive_aircraft.cargo_airship": "화물 비행선", "item.immersive_aircraft.warship": "워쉽", "item.immersive_aircraft.biplane": "복엽기", "item.immersive_aircraft.gyrodyne": "자이로다인", "item.immersive_aircraft.quadrocopter": "쿼드로콥터", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "느리고 연료가 많이 소모되지만 전체 저장 공간을 운반합니다.", "item.immersive_aircraft.warship.description": "느리지만 중무장한 하늘을 나는 요새입니다.", "item.immersive_aircraft.biplane.description": "빠르고 안정적입니다. 활주로가 충분히 긴지 확인하세요.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% 파워, 계속 밀어!", "immersive_aircraft.gyrodyne_target_reached": "최소 로터 속도 도달, 이륙 준비 완료!", "immersive_aircraft.invalid_dimension": "이 항공기는 이 차원에서는 작동하지 않습니다.", "immersive_aircraft.out_of_ammo": "탄약이 떨어졌어요!", "immersive_aircraft.repair": "%s%% 수리 완료!", "immersive_aircraft.tried_dismount": "다시 누르면 점프!", "immersive_aircraft.fuel.none": "연료가 없습니다!", "immersive_aircraft.fuel.out": "연료가 떨어졌습니다!", "immersive_aircraft.fuel.low": "연료가 부족합니다!", "immersive_aircraft.fat.none": "음식은 안 돼요!", "immersive_aircraft.fat.out": "너무 배가 고파서 날 수 없습니다!", "option.immersive_aircraft.general": "일반 옵션", "option.immersive_aircraft.separateCamera": "기내에서는 별도의 카메라를 사용하세요.", "option.immersive_aircraft.useThirdPersonByDefault": "기내에서는 기본적으로 3인칭 카메라로 설정됩니다.", "option.immersive_aircraft.enableTrails": "멋진 증기 트레일.", "option.immersive_aircraft.enableAnimatedSails": "파도치는 파도 돛.", "option.immersive_aircraft.renderDistance": "거리를 블록 단위로 렌더링합니다.", "option.immersive_aircraft.fuelConsumption": "연료 연소율.", "option.immersive_aircraft.windClearWeather": "기본 바람 효과.", "option.immersive_aircraft.windRainWeather": "강우 시 바람.", "option.immersive_aircraft.windThunderWeather": "천둥에 추가 바람.", "option.immersive_aircraft.repairSpeed": "클릭당 복구.", "option.immersive_aircraft.repairExhaustion": "클릭당 플레이어 피로도.", "option.immersive_aircraft.collisionDamage": "충돌 피해.", "option.immersive_aircraft.burnFuelInCreative": "크리에이티브 모드에서 연료를 태우세요.", "option.immersive_aircraft.acceptVanillaFuel": "바닐라 연료를 수락합니다.", "option.immersive_aircraft.useCustomKeybindSystem": "멀티 키 바인딩을 사용하면 특정 모드가 손상될 수 있습니다.", "option.immersive_aircraft.showHotbarEngineGauge": "핫바 위에 엔진 게이지를 렌더링합니다.", "option.immersive_aircraft.enableCrashExplosion": "폭발을 활성화합니다.", "option.immersive_aircraft.enableCrashBlockDestruction": "파괴적 폭발을 활성화합니다.", "option.immersive_aircraft.enableCrashFire": "화염 폭발을 활성화합니다.", "option.immersive_aircraft.crashExplosionRadius": "충돌 폭발의 크기입니다.", "option.immersive_aircraft.crashDamage": "충돌 시 플레이어에게 피해를 입힙니다.", "option.immersive_aircraft.preventKillThroughCrash": "충돌 시 플레이어를 죽이지 않습니다.", "option.immersive_aircraft.healthBarRow": "전차의 체력 바를 상쇄합니다.", "option.immersive_aircraft.damagePerHealthPoint": "값이 높을수록 항공기의 내구성이 높아집니다.", "option.immersive_aircraft.weaponsAreDestructive": "일부 무기로 블록을 파괴할 수 있도록 허용합니다.", "option.immersive_aircraft.dropInventory": "인벤토리를 항공기 아이템에 저장하지 않고 삭제합니다.", "option.immersive_aircraft.dropUpgrades": "업그레이드 및 커스터마이징을 항공기 아이템에 저장하는 대신 삭제합니다.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "주어진 틱마다 자동으로 체력을 재생성하여 바닐라 동작을 시뮬레이션합니다.", "option.immersive_aircraft.requireShiftForRepair": "교대 근무 시에만 수리하고, 그렇지 않으면 그냥 차량에 탑승하세요.", "immersive_aircraft.tooltip.no_target": "바닥에 조립해야 합니다!", "immersive_aircraft.tooltip.no_space": "공간이 부족합니다!", "immersive_aircraft.slot.booster": "부스트 로켓", "immersive_aircraft.slot.weapon": "무기 슬롯", "immersive_aircraft.slot.boiler": "연료 슬롯", "immersive_aircraft.slot.banner": "배너 슬롯", "immersive_aircraft.slot.dye": "염료 슬롯", "immersive_aircraft.slot.upgrade": "업그레이드 슬롯", "immersive_aircraft.upgrade.enginespeed": "%s엔진 출력 %%", "immersive_aircraft.upgrade.friction": "%s공기 마찰 %%", "immersive_aircraft.upgrade.acceleration": "%s이륙 속도 %%", "immersive_aircraft.upgrade.durability": "%s%% 내구성", "immersive_aircraft.upgrade.fuel": "%s연료 요구량 %%", "immersive_aircraft.upgrade.wind": "%s바람 효과 %%", "immersive_aircraft.upgrade.stabilizer": "%s%% 안정화 중", "immersive_aircraft.tooltip.inventory": "%s 항목이 포함되어 있습니다."}