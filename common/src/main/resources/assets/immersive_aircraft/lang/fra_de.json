{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Flieger", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Vorwärts", "key.immersive_aircraft.multi_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_down": "Unten", "key.immersive_aircraft.multi_control_pull": "Controller ziehen", "key.immersive_aircraft.multi_control_push": "Controller d<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_use": "Waffe/Anbau verwenden", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Vorwärts", "key.immersive_aircraft.fallback_control_backward": "Rück<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_down": "Unten", "key.immersive_aircraft.fallback_control_pull": "Controller ziehen", "key.immersive_aircraft.fallback_control_push": "Controller d<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_use": "Waffe/Anbau verwenden", "key.immersive_aircraft.dismount": "Absteigen", "key.immersive_aircraft.boost": "Raketen-Boost", "entity.immersive_aircraft.airship": "Luftschiff", "entity.immersive_aircraft.cargo_airship": "Frachtluftschiff", "entity.immersive_aircraft.warship": "Kriegsschiff", "entity.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Kessel", "item.immersive_aircraft.enhanced_propeller": "Verbesserter Propeller", "item.immersive_aircraft.eco_engine": "Öko-Motor", "item.immersive_aircraft.nether_engine": "Nether-Motor", "item.immersive_aircraft.steel_boiler": "Stahlkessel", "item.immersive_aircraft.industrial_gears": "Industrielle <PERSON>", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Rumpforstärkung", "item.immersive_aircraft.improved_landing_gear": "Verbessertes Fahrwerk", "item.immersive_aircraft.rotary_cannon": "Rotationskanone", "item.immersive_aircraft.bomb_bay": "Bombenraum", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Schwere Armbrust", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON>uer<PERSON><PERSON>, die mit Schießpulver betrieben wird.", "item.immersive_aircraft.bomb_bay.description": "Setzt TNT ab, zerstört keine Blöcke, verursacht jedoch hohen Schaden.", "item.immersive_aircraft.telescope.description": "Eine kompaktere Version des Fernrohrs.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>e schwere Armbrust mit viel Wucht, <PERSON><PERSON><PERSON><PERSON>.", "item.immersive_aircraft.item.upgrade": "Flugzeug-Upgrade", "item.immersive_aircraft.item.weapon": "Flugzeugwaffe", "item.immersive_aircraft.airship": "Luftschiff", "item.immersive_aircraft.cargo_airship": "Frachtluftschiff", "item.immersive_aircraft.warship": "Kriegsschiff", "item.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Langsam und spritfressend, aber trägt eine gesamte Lagerung.", "item.immersive_aircraft.warship.description": "Eine fliegende Festung, lang<PERSON><PERSON>, aber schwer bewaffnet.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON><PERSON> und ziemlich zuverlässig. <PERSON><PERSON> sicher, dass deine Landebahn lang genug ist.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Leistung, weitermachen!", "immersive_aircraft.gyrodyne_target_reached": "Minimale Rotor-<PERSON><PERSON><PERSON><PERSON> erreicht, bereit zum Abheben!", "immersive_aircraft.invalid_dimension": "Dieses Flugzeug funktioniert in dieser Dimension nicht.", "immersive_aircraft.out_of_ammo": "Aus Munition!", "immersive_aircraft.repair": "%s%% repariert!", "immersive_aircraft.tried_dismount": "Drücke erneut um abzuspringen!", "immersive_aircraft.fuel.none": "<PERSON><PERSON>!", "immersive_aircraft.fuel.out": "Dir ist der Brennstoff ausgegangen!", "immersive_aircraft.fuel.low": "Du hast wenig Bren<PERSON>off!", "immersive_aircraft.fat.none": "<PERSON><PERSON>!", "immersive_aircraft.fat.out": "Du bist zu hungrig zum Fliegen!", "option.immersive_aircraft.general": "Allgemeine Optionen", "option.immersive_aircraft.separateCamera": "Verwende eine separate Kamera im Flugzeug.", "option.immersive_aircraft.useThirdPersonByDefault": "Standardmäßig auf dritte Person Kamera im Flugzeug.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON>s <PERSON>pfschwaden.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON>ig, wellig <PERSON>.", "option.immersive_aircraft.renderDistance": "Renderdistanz in Blöcken.", "option.immersive_aircraft.fuelConsumption": "Brennstoffverbrauch.", "option.immersive_aircraft.windClearWeather": "Basis-Wind-Effekt.", "option.immersive_aircraft.windRainWeather": "Wind bei Regen.", "option.immersive_aircraft.windThunderWeather": "Zusätzlicher Wind bei Donner.", "option.immersive_aircraft.repairSpeed": "Reparatur pro Klick.", "option.immersive_aircraft.repairExhaustion": "Spielerermüdung pro Klick.", "option.immersive_aircraft.collisionDamage": "Kollisionsschaden.", "option.immersive_aircraft.burnFuelInCreative": "Brennstoff im kreativen Modus verbrauchen.", "option.immersive_aircraft.acceptVanillaFuel": "Akzeptier<PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "Verwende mehrere Tastenbelegungen, kann bestimmte Mods beeinträchtigen.", "option.immersive_aircraft.showHotbarEngineGauge": "Zeige das Motoranzeigegerät über der Hotbar an.", "option.immersive_aircraft.enableCrashExplosion": "Explosion aktivieren.", "option.immersive_aircraft.enableCrashBlockDestruction": "Zerstörerische Explosion aktivieren.", "option.immersive_aircraft.enableCrashFire": "Feurige Explosion aktivieren.", "option.immersive_aircraft.crashExplosionRadius": "Größe der Crash-Explosion.", "option.immersive_aircraft.crashDamage": "Verletzt den Spieler bei einem Absturz.", "option.immersive_aircraft.preventKillThroughCrash": "Tötet den Spieler nicht bei einem Absturz.", "option.immersive_aircraft.healthBarRow": "Gesundheitsbalken von Fahrzeugen anpassen.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON> We<PERSON> machen Flugzeuge haltbarer.", "option.immersive_aircraft.weaponsAreDestructive": "Erlaube einigen W<PERSON>fen, <PERSON><PERSON><PERSON><PERSON> zu zerstören.", "option.immersive_aircraft.dropInventory": "Lass das Inventar fallen, anstatt es im Flugobjekt zu speichern.", "option.immersive_aircraft.dropUpgrades": "Lass die Upgrades und Anpassungen fallen, anstatt sie im Flugobjekt zu speichern.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneriert automatisch die Gesundheit bei jedem gegebenen Tick und simuliert das Vanilleverhalten.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON> reparieren, wenn die Umschalttaste gedrückt wird, ansonsten einfach das Fahrzeug betreten.", "immersive_aircraft.tooltip.no_target": "Muss auf dem Boden zusammengebaut werden!", "immersive_aircraft.tooltip.no_space": "Nicht genug Platz!", "immersive_aircraft.slot.booster": "Boost-Raketen", "immersive_aircraft.slot.weapon": "Waffenplatz", "immersive_aircraft.slot.boiler": "Brennstoffslot", "immersive_aircraft.slot.banner": "Banner-Slot", "immersive_aircraft.slot.dye": "Färbeslot", "immersive_aircraft.slot.upgrade": "Upgrade-Slot", "immersive_aircraft.upgrade.enginespeed": "%s%% Motorleistung", "immersive_aircraft.upgrade.friction": "%s%% Luftreibung", "immersive_aircraft.upgrade.acceleration": "%s%% Startgeschwindigkeit", "immersive_aircraft.upgrade.durability": "%s%% Haltbarkeit", "immersive_aircraft.upgrade.fuel": "%s%% Brennstoffbedarf", "immersive_aircraft.upgrade.wind": "%s%% Windeffekt", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilisierend", "immersive_aircraft.tooltip.inventory": "Enthält %s Gegenstände."}