{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "Left", "key.immersive_aircraft.multi_control_right": "Right", "key.immersive_aircraft.multi_control_forward": "Forward", "key.immersive_aircraft.multi_control_backward": "Backward", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Use weapon/mount", "key.immersive_aircraft.fallback_control_left": "Left", "key.immersive_aircraft.fallback_control_right": "Right", "key.immersive_aircraft.fallback_control_forward": "Forward", "key.immersive_aircraft.fallback_control_backward": "Backward", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Use weapon/mount", "key.immersive_aircraft.dismount": "Dismount", "key.immersive_aircraft.boost": "Rocket boost", "entity.immersive_aircraft.airship": "Airship", "entity.immersive_aircraft.cargo_airship": "Cargo Airship", "entity.immersive_aircraft.warship": "Warship", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadcopter", "item.immersive_aircraft.hull": "Hull", "item.immersive_aircraft.engine": "Engine", "item.immersive_aircraft.sail": "Sail", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Enhan<PERSON>", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Steel Boiler", "item.immersive_aircraft.industrial_gears": "Industrial Gears", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroscope", "item.immersive_aircraft.hull_reinforcement": "Hull Reinforcement", "item.immersive_aircraft.improved_landing_gear": "Improved Landing Gear", "item.immersive_aircraft.rotary_cannon": "Rotary Cannon", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "Telescope", "item.immersive_aircraft.heavy_crossbow": "Heavy Crossbow", "item.immersive_aircraft.rotary_cannon.description": "Fast-firing cannon that operates with gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Drops TNT, does not destroy blocks but inflicts heavy damage.", "item.immersive_aircraft.telescope.description": "A bulkier version of the spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "A heavy crossbow with significant power, requires arrows.", "item.immersive_aircraft.item.upgrade": "Aircraft Upgrade", "item.immersive_aircraft.item.weapon": "Aircraft Weapon", "item.immersive_aircraft.airship": "Airship", "item.immersive_aircraft.cargo_airship": "Cargo Airship", "item.immersive_aircraft.warship": "Warship", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadcopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Slow and fuel-hungry but carries an entire storage.", "item.immersive_aircraft.warship.description": "A flying fortress, slow but heavily armed.", "item.immersive_aircraft.biplane.description": "This trusty, rustic biplane will take you anywhere fast and rather reliably. Make sure your runway is long enough.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Power, keep pushing!", "immersive_aircraft.gyrodyne_target_reached": "Minimum rotor speed reached, ready for takeoff!", "immersive_aircraft.invalid_dimension": "This aircraft does not operate in this dimension.", "immersive_aircraft.out_of_ammo": "Out of ammo!", "immersive_aircraft.repair": "%s%% repaired!", "immersive_aircraft.tried_dismount": "Press again to jump out!", "immersive_aircraft.fuel.none": "No fuel!", "immersive_aircraft.fuel.out": "You have run out of fuel!", "immersive_aircraft.fuel.low": "You are low on fuel!", "immersive_aircraft.fat.none": "No food!", "immersive_aircraft.fat.out": "You are too hungry to fly!", "option.immersive_aircraft.general": "General Options", "option.immersive_aircraft.separateCamera": "Use a separate camera in aircraft.", "option.immersive_aircraft.useThirdPersonByDefault": "Defaults to third person camera in aircraft.", "option.immersive_aircraft.enableTrails": "Fancy steam trails.", "option.immersive_aircraft.enableAnimatedSails": "Wavy sails.", "option.immersive_aircraft.renderDistance": "Render distance in blocks.", "option.immersive_aircraft.fuelConsumption": "Fuel burn rate.", "option.immersive_aircraft.windClearWeather": "Base wind effect.", "option.immersive_aircraft.windRainWeather": "Wind during rainfall.", "option.immersive_aircraft.windThunderWeather": "Extra wind during thunderstorms.", "option.immersive_aircraft.repairSpeed": "Repair per click.", "option.immersive_aircraft.repairExhaustion": "Player exhaustion per click.", "option.immersive_aircraft.collisionDamage": "Collision damage.", "option.immersive_aircraft.burnFuelInCreative": "Burn fuel in creative mode.", "option.immersive_aircraft.acceptVanillaFuel": "Accept vanilla fuel.", "option.immersive_aircraft.useCustomKeybindSystem": "Use multi-keybindings, may break certain mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Render the engine gauge over the hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Enable explosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Enable destructive explosion.", "option.immersive_aircraft.enableCrashFire": "Enable fiery explosion.", "option.immersive_aircraft.crashExplosionRadius": "Size of crash explosion.", "option.immersive_aircraft.crashDamage": "Damages the player on a crash.", "option.immersive_aircraft.preventKillThroughCrash": "Will not kill the player upon crashing.", "option.immersive_aircraft.healthBarRow": "Offset the health bar of vehicles.", "option.immersive_aircraft.damagePerHealthPoint": "Higher values make aircraft more durable.", "option.immersive_aircraft.weaponsAreDestructive": "Allow some weapons to destroy blocks.", "option.immersive_aircraft.dropInventory": "Drop the inventory instead of saving it within the aircraft item.", "option.immersive_aircraft.dropUpgrades": "Drop the upgrades and customisation instead of saving it within the aircraft item.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatically regenerates health every given tick, simulating vanilla behaviour.", "option.immersive_aircraft.requireShiftForRepair": "Only repair when holding shift, otherwise just enter the vehicle.", "immersive_aircraft.tooltip.no_target": "Needs to be assembled on the floor!", "immersive_aircraft.tooltip.no_space": "Not enough space!", "immersive_aircraft.slot.booster": "Boost rockets", "immersive_aircraft.slot.weapon": "Weapon slot", "immersive_aircraft.slot.boiler": "Fuel slot", "immersive_aircraft.slot.banner": "Banner slot", "immersive_aircraft.slot.dye": "Dye slot", "immersive_aircraft.slot.upgrade": "Upgrade slot", "immersive_aircraft.upgrade.enginespeed": "%s%% engine power", "immersive_aircraft.upgrade.friction": "%s%% air friction", "immersive_aircraft.upgrade.acceleration": "%s%% takeoff speed", "immersive_aircraft.upgrade.durability": "%s%% durability", "immersive_aircraft.upgrade.fuel": "%s%% fuel requirement", "immersive_aircraft.upgrade.wind": "%s%% wind effect", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilising", "immersive_aircraft.tooltip.inventory": "Contains %s items."}