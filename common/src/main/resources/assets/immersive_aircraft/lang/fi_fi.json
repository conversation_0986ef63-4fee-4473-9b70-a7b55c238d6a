{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersiivinen ilma-alus", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Käytä asetta/mountia", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Käytä asetta/mountia", "key.immersive_aircraft.dismount": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.boost": "Rakettitehostin", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.warship": "So<PERSON>-alus", "entity.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON>", "item.immersive_aircraft.engine": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Pot<PERSON><PERSON>", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Parannettu potkuri", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Teräskattila", "item.immersive_aircraft.industrial_gears": "Teollisuuden hammaspyörät", "item.immersive_aircraft.sturdy_pipes": "<PERSON>kevat putket", "item.immersive_aircraft.gyroscope": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull_reinforcement": "Rungon vahvistaminen", "item.immersive_aircraft.improved_landing_gear": "Parannettu laskuteline", "item.immersive_aircraft.rotary_cannon": "Pyörivä tykki", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON>i ampuva t<PERSON>, joka to<PERSON>ii ru<PERSON>.", "item.immersive_aircraft.bomb_bay.description": "Pudottaa TNT:tä, ei tuhoa <PERSON>, mutta aiheuttaa raskasta vahi<PERSON>a.", "item.immersive_aircraft.telescope.description": "Vankempi versio <PERSON>.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON><PERSON><PERSON>, jolla on voimakas isku, vaatii nuolia.", "item.immersive_aircraft.item.upgrade": "Ilma-al<PERSON><PERSON> p<PERSON>s", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.warship": "So<PERSON>-alus", "item.immersive_aircraft.biplane": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Ilmalaivat eivät ehkä ole nopein kulku<PERSON>uvo, mutta niitä on <PERSON><PERSON>.", "item.immersive_aircraft.cargo_airship.description": "<PERSON><PERSON> ja p<PERSON><PERSON><PERSON><PERSON>, mutta kantaa koko var<PERSON>.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>, hidas mutta raskaasti as<PERSON>.", "item.immersive_aircraft.biplane.description": "<PERSON>a ja melko luo<PERSON>. Varmista, että kiitotie on riittävän pitkä.", "item.immersive_aircraft.gyrodyne.description": "<PERSON><PERSON> tarvi<PERSON><PERSON> moot<PERSON>, jos lent<PERSON> voisi pyörittä<PERSON> pelkällä voimalla? Anna sille kunnon työntö ja se lentää!", "item.immersive_aircraft.quadrocopter.description": "Insinöörityön mestariteos! 4 roottoria kiinnitettynä bambuun. Täydellinen rakenta<PERSON>en, ja siinä kaik<PERSON>.", "immersive_aircraft.gyrodyne_target": "%d%% Voimaa, jatka ponnistelua!", "immersive_aircraft.gyrodyne_target_reached": "<PERSON><PERSON>in vähimmäisnopeus sa<PERSON>u, val<PERSON><PERSON> lent<PERSON>!", "immersive_aircraft.invalid_dimension": "Tä<PERSON>ä lentokone ei toimi tässä ulottuvuudessa.", "immersive_aircraft.out_of_ammo": "Ammukset loppu!", "immersive_aircraft.repair": "%s%% korjattu!", "immersive_aircraft.tried_dismount": "Hyppää ulos pain<PERSON> uudelleen!", "immersive_aircraft.fuel.none": "Ei polt<PERSON>!", "immersive_aircraft.fuel.out": "<PERSON><PERSON><PERSON> loppui!", "immersive_aircraft.fuel.low": "Pol<PERSON>aine on vähissä!", "immersive_aircraft.fat.none": "Ei ruokaa!", "immersive_aircraft.fat.out": "Olet liian nälkäinen lentääksesi!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "option.immersive_aircraft.separateCamera": "Käytä lentokoneessa erillistä kameraa.", "option.immersive_aircraft.useThirdPersonByDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kolmannen persoonan kamera lent<PERSON>.", "option.immersive_aircraft.enableTrails": "Hienoja höyrypolkuja.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>.", "option.immersive_aircraft.renderDistance": "Renderöintietäisyys lohkoina.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON><PERSON><PERSON> palamis<PERSON>.", "option.immersive_aircraft.windClearWeather": "Po<PERSON><PERSON><PERSON><PERSON>va<PERSON><PERSON><PERSON>.", "option.immersive_aircraft.windRainWeather": "<PERSON><PERSON>.", "option.immersive_aircraft.windThunderWeather": "Lisätuulta <PERSON>.", "option.immersive_aircraft.repairSpeed": "<PERSON><PERSON><PERSON><PERSON> per klikkaus.", "option.immersive_aircraft.repairExhaustion": "P<PERSON>ajan u<PERSON> per klikkaus.", "option.immersive_aircraft.collisionDamage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.burnFuelInCreative": "Polta polt<PERSON>ainetta luovassa tilassa.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "Käytä useita a<PERSON>insidoksia, saattaa rikkoa tiettyj<PERSON> modeja.", "option.immersive_aircraft.showHotbarEngineGauge": "Renderöi moottorin mittari kuuma<PERSON> yl<PERSON>lle.", "option.immersive_aircraft.enableCrashExplosion": "<PERSON><PERSON> r<PERSON><PERSON> käyttöön.", "option.immersive_aircraft.enableCrashBlockDestruction": "<PERSON><PERSON><PERSON> r<PERSON>jähdyksen ma<PERSON>llis<PERSON>n.", "option.immersive_aircraft.enableCrashFire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tuli<PERSON> r<PERSON><PERSON>.", "option.immersive_aircraft.crashExplosionRadius": "Törmäysräjähdyksen koko.", "option.immersive_aircraft.crashDamage": "Vahingoittaa pelaajaa kaat<PERSON>sen yhteydessä.", "option.immersive_aircraft.preventKillThroughCrash": "Ei tapa pelaajaa kaatumisen yhteydessä.", "option.immersive_aircraft.healthBarRow": "Poistaa ajoneuvoje<PERSON>.", "option.immersive_aircraft.damagePerHealthPoint": "Suuremmat arvot tekevät lentokoneesta kestävämmän.", "option.immersive_aircraft.weaponsAreDestructive": "<PERSON><PERSON> j<PERSON> as<PERSON>den tuhota lo<PERSON>.", "option.immersive_aircraft.dropInventory": "Pudota inventaario sen sijaan, että tallentaisit sen lentokoneen kohteeseen.", "option.immersive_aircraft.dropUpgrades": "Pudota päivitykset ja räätälöinnit sen sijaan, että tallentaisit ne lentokoneen kohteen sisään.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Uudistaa automaattisesti terveyttä joka tikillä, mik<PERSON> simuloi vaniljakäyttäytymistä.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON><PERSON><PERSON> vain, kun pidät vai<PERSON><PERSON>, muuten vain astu ajon<PERSON>.", "immersive_aircraft.tooltip.no_target": "<PERSON><PERSON><PERSON> koota lattialla!", "immersive_aircraft.tooltip.no_space": "Ei tarpeeksi tilaa!", "immersive_aircraft.slot.booster": "Boost-raketit", "immersive_aircraft.slot.weapon": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.boiler": "Polttoainepaikka", "immersive_aircraft.slot.banner": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.dye": "Väriaine-aukko", "immersive_aircraft.slot.upgrade": "Päivityspaikka", "immersive_aircraft.upgrade.enginespeed": "%s%% moottorin teho", "immersive_aircraft.upgrade.friction": "%s%% ilman kitka", "immersive_aircraft.upgrade.acceleration": "%s%% lentoonlähtönopeus", "immersive_aircraft.upgrade.durability": "%s%% kestävyys", "immersive_aircraft.upgrade.fuel": "%s% % polttoaineen tarve", "immersive_aircraft.upgrade.wind": "%s%% tuulen vaikutus", "immersive_aircraft.upgrade.stabilizer": "%s%% vakauttaminen", "immersive_aircraft.tooltip.inventory": "Sisältää %s ."}