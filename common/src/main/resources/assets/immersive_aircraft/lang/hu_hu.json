{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "Balra", "key.immersive_aircraft.multi_control_right": "Jobbra", "key.immersive_aircraft.multi_control_forward": "Előre", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_pull": "Húzásvezérlő", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Fegyverhasználat/mount", "key.immersive_aircraft.fallback_control_left": "Balra", "key.immersive_aircraft.fallback_control_right": "Jobbra", "key.immersive_aircraft.fallback_control_forward": "Előre", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_down": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_pull": "Húzásvezérlő", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Fegyverhasználat/mount", "key.immersive_aircraft.dismount": "Leszerelés", "key.immersive_aircraft.boost": "Rocket boost", "entity.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.warship": "<PERSON><PERSON><PERSON><PERSON>", "entity.immersive_aircraft.biplane": "Kétfedelű repülőgép", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Hull", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Vitorla", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Kazán", "item.immersive_aircraft.enhanced_propeller": "Továbbfejlesztett légcsavar", "item.immersive_aircraft.eco_engine": "Eco motor", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Acél <PERSON>", "item.immersive_aircraft.industrial_gears": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.sturdy_pipes": "Stabil csövek", "item.immersive_aircraft.gyroscope": "Gyroszkóp", "item.immersive_aircraft.hull_reinforcement": "Hull megerősítés", "item.immersive_aircraft.improved_landing_gear": "Továbbfejlesztett futómű", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "Távcső", "item.immersive_aircraft.heavy_crossbow": "Nehéz nyílpuska", "item.immersive_aircraft.rotary_cannon.description": "Puskaporral működő, gyorsan tüzelő ágyú.", "item.immersive_aircraft.bomb_bay.description": "TNT-t dob le, nem pusztítja el a blokkokat, de s<PERSON>yos se<PERSON><PERSON> ok<PERSON>.", "item.immersive_aircraft.telescope.description": "A távcső terjedelmesebb változata.", "item.immersive_aircraft.heavy_crossbow.description": "Nehéz nyílpuska er<PERSON><PERSON>, am<PERSON><PERSON><PERSON> van szükség.", "item.immersive_aircraft.item.upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "item.immersive_aircraft.item.weapon": "Repülőgép fegyver", "item.immersive_aircraft.airship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.warship": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.biplane": "Kétfedelű repülőgép", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "A léghajók talán nem a leggyorsabb járművek, de az biztos, hogy könnyen manőverezhetők.", "item.immersive_aircraft.cargo_airship.description": "Lassú és üzemanyag-<PERSON>, de egy egész raktárat sz<PERSON>llí<PERSON>.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, de j<PERSON><PERSON> felfegyver<PERSON>tt.", "item.immersive_aircraft.biplane.description": "Gyors és meglehetősen megbízható. Győződjön meg róla, hogy a kifutópálya elég hosszú.", "item.immersive_aircraft.gyrodyne.description": "<PERSON><PERSON><PERSON> van szüksége motorra, ha a gépet puszta erővel is lehet hajtani? Adj neki egy jó <PERSON>, és már repül is!", "item.immersive_aircraft.quadrocopter.description": "A mérnöki munka mesterműve! 4 forgórész bambuszra erősítve. Tökéletes az építéshez, és ennyi.", "immersive_aircraft.gyrodyne_target": "%d%% Power, nyomd tovább!", "immersive_aircraft.gyrodyne_target_reached": "Minimális <PERSON>besség elérve, felszállásra kész!", "immersive_aircraft.invalid_dimension": "Ez a repülőgép nem működik ebben a dimenzióban.", "immersive_aircraft.out_of_ammo": "Elfogyott a lőszer!", "immersive_aircraft.repair": "%s%% javítva!", "immersive_aircraft.tried_dismount": "<PERSON>yo<PERSON>d meg <PERSON>, hogy kiu<PERSON>j!", "immersive_aircraft.fuel.none": "Nincs üzemanyag!", "immersive_aircraft.fuel.out": "Kifogyott az üzemanyag!", "immersive_aircraft.fuel.low": "Kevés az üzemanyag!", "immersive_aircraft.fat.none": "<PERSON>nc<PERSON> kaja!", "immersive_aircraft.fat.out": "Túl éhes vagy a repüléshez!", "option.immersive_aircraft.general": "Általános le<PERSON>őségek", "option.immersive_aircraft.separateCamera": "Használjon külön kamerát a repülőgépen.", "option.immersive_aircraft.useThirdPersonByDefault": "Alapértelmezett harmadik személyű kamera a repülőgépen.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON>.", "option.immersive_aircraft.enableAnimatedSails": "Hullámos <PERSON> v<PERSON>.", "option.immersive_aircraft.renderDistance": "Renderelési távolság blokkokban.", "option.immersive_aircraft.fuelConsumption": "Üzemanyag-é<PERSON><PERSON>.", "option.immersive_aircraft.windClearWeather": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.windRainWeather": "Szél az esőzéskor.", "option.immersive_aircraft.windThunderWeather": "Extra szél a mennydörgésnél.", "option.immersive_aircraft.repairSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>.", "option.immersive_aircraft.repairExhaustion": "Játékos kimerülése ka<PERSON>.", "option.immersive_aircraft.collisionDamage": "Ütközési kár.", "option.immersive_aircraft.burnFuelInCreative": "Égess üzemanyagot kreatív üzemmódban.", "option.immersive_aircraft.acceptVanillaFuel": "Fogadja el a vaníliás üzemanyagot.", "option.immersive_aircraft.useCustomKeybindSystem": "<PERSON><PERSON><PERSON><PERSON><PERSON> több kulcsot is, ez bizonyos modokat megtörhet.", "option.immersive_aircraft.showHotbarEngineGauge": "A motorszintmérő megjelenítése a hotbar fölött.", "option.immersive_aircraft.enableCrashExplosion": "Robbanás engedélyezése.", "option.immersive_aircraft.enableCrashBlockDestruction": "Pusztító robbanás engedélyezése.", "option.immersive_aircraft.enableCrashFire": "<PERSON><PERSON>zes robbanás lehetővé tétele.", "option.immersive_aircraft.crashExplosionRadius": "Az ütközésrobbanás mérete.", "option.immersive_aircraft.crashDamage": "A játékos sérülése egy ütközéskor.", "option.immersive_aircraft.preventKillThroughCrash": "Nem <PERSON> meg a játékost összeomláskor.", "option.immersive_aircraft.healthBarRow": "<PERSON><PERSON><PERSON><PERSON><PERSON> a járművek egészségsávját.", "option.immersive_aircraft.damagePerHealthPoint": "A magasabb értékek tartósabbá teszik a repülőgépeket.", "option.immersive_aircraft.weaponsAreDestructive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy egyes fegyverek blokkokat semmisítsenek meg.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON><PERSON> el a le<PERSON>, <PERSON><PERSON><PERSON>, hogy a repülőgépelemen belül mentené el.", "option.immersive_aircraft.dropUpgrades": "Dobja el a frissítéseket és a testreszab<PERSON>, ah<PERSON><PERSON>, hogy a repülőgépelemen belül menten<PERSON> el.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatikusan regenerálja az egészséget minden adott tick<PERSON>, szi<PERSON><PERSON><PERSON><PERSON><PERSON> a van<PERSON><PERSON> v<PERSON>.", "option.immersive_aircraft.requireShiftForRepair": "Csak a<PERSON><PERSON>, ha a váltót lenyomva tartja, e<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> csak lépjen be a járműbe.", "immersive_aircraft.tooltip.no_target": "A padlón kell összeszerelni!", "immersive_aircraft.tooltip.no_space": "Nincs elég hely!", "immersive_aircraft.slot.booster": "<PERSON><PERSON>", "immersive_aircraft.slot.weapon": "Fegyver nyílás", "immersive_aircraft.slot.boiler": "Üzemanya<PERSON>", "immersive_aircraft.slot.banner": "Banner slot", "immersive_aircraft.slot.dye": "Színezőnyílás", "immersive_aircraft.slot.upgrade": "Frissítési nyílás", "immersive_aircraft.upgrade.enginespeed": "%s%% motorteljesítmény", "immersive_aircraft.upgrade.friction": "%s%% légsúrlódás", "immersive_aircraft.upgrade.acceleration": "%s%% felszállási sebesség", "immersive_aircraft.upgrade.durability": "%s%% tartósság", "immersive_aircraft.upgrade.fuel": "%s%% üzemanyag-szükséglet", "immersive_aircraft.upgrade.wind": "%s%% szélhatás", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizálás", "immersive_aircraft.tooltip.inventory": "Tartalmazza a %s elemeket."}