{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Vliegtuigen", "key.immersive_aircraft.multi_control_left": "Links", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Vooruit", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "Omhoog", "key.immersive_aircraft.multi_control_down": "Omlaag", "key.immersive_aircraft.multi_control_pull": "Trek Controller", "key.immersive_aircraft.multi_control_push": "Duw Controller", "key.immersive_aircraft.multi_use": "Gebruik wapen/montage", "key.immersive_aircraft.fallback_control_left": "Links", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Vooruit", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "Omhoog", "key.immersive_aircraft.fallback_control_down": "Omlaag", "key.immersive_aircraft.fallback_control_pull": "Trek Controller", "key.immersive_aircraft.fallback_control_push": "Duw Controller", "key.immersive_aircraft.fallback_use": "Gebruik wapen/montage", "key.immersive_aircraft.dismount": "Afsteige", "key.immersive_aircraft.boost": "Raket boost", "entity.immersive_aircraft.airship": "Luftschip", "entity.immersive_aircraft.cargo_airship": "Cargo-luchtvaartschip", "entity.immersive_aircraft.warship": "Oorlogsschip", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Rom", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "<PERSON><PERSON>", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Verbeterde Propeller", "item.immersive_aircraft.eco_engine": "Eco Motor", "item.immersive_aircraft.nether_engine": "Nether Motor", "item.immersive_aircraft.steel_boiler": "Stalen <PERSON>", "item.immersive_aircraft.industrial_gears": "Industriël<PERSON>", "item.immersive_aircraft.sturdy_pipes": "Sterke Buizen", "item.immersive_aircraft.gyroscope": "Gyroscoop", "item.immersive_aircraft.hull_reinforcement": "Rompw versterking", "item.immersive_aircraft.improved_landing_gear": "Verbeterde Landingsgestel", "item.immersive_aircraft.rotary_cannon": "Rotonde Kanon", "item.immersive_aircraft.bomb_bay": "Bom Bay", "item.immersive_aircraft.telescope": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.heavy_crossbow": "Zware kruisboog", "item.immersive_aircraft.rotary_cannon.description": "Snel-vurende kanon dat met buskruit werkt.", "item.immersive_aircraft.bomb_bay.description": "Laat TNT vallen, vernietigt geen blokken maar doet zware schade.", "item.immersive_aircraft.telescope.description": "<PERSON><PERSON> stevigere versie van de verrekijker.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>en z<PERSON> k<PERSON> met een krachtige klap, vereist pijlen.", "item.immersive_aircraft.item.upgrade": "Luchtvaartuig Upgrade", "item.immersive_aircraft.item.weapon": "Vliegtuigwapen", "item.immersive_aircraft.airship": "Luftschip", "item.immersive_aircraft.cargo_airship": "Cargo-luchtvaartschip", "item.immersive_aircraft.warship": "Oorlogsschip", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Langzaam en brands<PERSON>fhong<PERSON>g, maar draagt een hele opslag.", "item.immersive_aircraft.warship.description": "E vliegjende fort, traag mer heel geweldich bewapend.", "item.immersive_aircraft.biplane.description": "Snel en vrij betrouwbaar. Zorg ervoor dat je <PERSON>baan lang genoeg is.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% <PERSON><PERSON><PERSON>, blief duwen!", "immersive_aircraft.gyrodyne_target_reached": "Minimale rotor snelheid bereikt, gereed voor opstijgen!", "immersive_aircraft.invalid_dimension": "Dit vliegtuig werkt niet in deze dimensie.", "immersive_aircraft.out_of_ammo": "Geen munitie!", "immersive_aircraft.repair": "%s%% gerepareerd!", "immersive_aircraft.tried_dismount": "Druk opnieuw om eruit te springen!", "immersive_aircraft.fuel.none": "<PERSON>n brandstof!", "immersive_aircraft.fuel.out": "Je hebt geen brands<PERSON><PERSON> meer!", "immersive_aircraft.fuel.low": "Do hebt te weinich fuel!", "immersive_aircraft.fat.none": "Geit net te et'n!", "immersive_aircraft.fat.out": "Do bist te hongerich om te vlüge!", "option.immersive_aircraft.general": "Algemene Opties", "option.immersive_aircraft.separateCamera": "Gebruik een aparte camera in het luchtvaartuig.", "option.immersive_aircraft.useThirdPersonByDefault": "Standaard derde-persoons camera in het luchtvaartuig.", "option.immersive_aircraft.enableTrails": "Fijne stoomsporen.", "option.immersive_aircraft.enableAnimatedSails": "Golvend zeil.", "option.immersive_aircraft.renderDistance": "Render afstand in blokken.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON>f verbruik.", "option.immersive_aircraft.windClearWeather": "Basis wind effect.", "option.immersive_aircraft.windRainWeather": "Wind bij regen.", "option.immersive_aircraft.windThunderWeather": "Extra wind bij donder.", "option.immersive_aircraft.repairSpeed": "Repareren per klik.", "option.immersive_aircraft.repairExhaustion": "<PERSON>peler vermoeidheid per klik.", "option.immersive_aircraft.collisionDamage": "Botsingschade.", "option.immersive_aircraft.burnFuelInCreative": "Brand fuel in kreative modus.", "option.immersive_aircraft.acceptVanillaFuel": "Accepteer vanilla-fuel.", "option.immersive_aircraft.useCustomKeybindSystem": "Gebruik multi-toetsbindings, kan bepaalde mods verbreken.", "option.immersive_aircraft.showHotbarEngineGauge": "Toon de motormeter boven de hotbar.", "option.immersive_aircraft.enableCrashExplosion": "<PERSON><PERSON><PERSON> explosie in.", "option.immersive_aircraft.enableCrashBlockDestruction": "<PERSON><PERSON><PERSON> destructieve explosie in.", "option.immersive_aircraft.enableCrashFire": "<PERSON><PERSON><PERSON> vlammen explosie in.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON> van de crash explosie.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON><PERSON> a<PERSON> de s<PERSON>er bij een crash.", "option.immersive_aircraft.preventKillThroughCrash": "<PERSON><PERSON> de speler niet doden bij een crash.", "option.immersive_aircraft.healthBarRow": "Offset de gezondheidsbalk van voertuigen.", "option.immersive_aircraft.damagePerHealthPoint": "Hogere waarden maken vliegtuigen duurzamer.", "option.immersive_aircraft.weaponsAreDestructive": "Laat einige bewapening blokke vernielen.", "option.immersive_aircraft.dropInventory": "Laat de inventaris vallen, in plaats van het binnenin het luchtvaartuig te bewaren.", "option.immersive_aircraft.dropUpgrades": "Laat de upgrades en personalisatie vallen, in plaats van het binnenin het luchtvaartuig te bewaren.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regeneert automatisch gezondheid elke gegeven tick, wat de standaardgedrag simuleert.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON><PERSON> repareren terwijl je shift houdt, anders stap gewoon in het voertuig.", "immersive_aircraft.tooltip.no_target": "Moet op de vloer geassembleerd worden!", "immersive_aircraft.tooltip.no_space": "<PERSON>et geno<PERSON> rui<PERSON>!", "immersive_aircraft.slot.booster": "Boost raketten", "immersive_aircraft.slot.weapon": "<PERSON><PERSON><PERSON> slot", "immersive_aircraft.slot.boiler": "Brandstofslot", "immersive_aircraft.slot.banner": "Banner-slot", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Upgrade-slot", "immersive_aircraft.upgrade.enginespeed": "%s%% motor kracht", "immersive_aircraft.upgrade.friction": "%s%% lucht wrijving", "immersive_aircraft.upgrade.acceleration": "%s%% opstijg snelheid", "immersive_aircraft.upgrade.durability": "%s%% duurzaamheid", "immersive_aircraft.upgrade.fuel": "%s%% brandstof vereiste", "immersive_aircraft.upgrade.wind": "%s%% wind effect", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliserend", "immersive_aircraft.tooltip.inventory": "Bevat %s items."}