{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircrafft", "key.immersive_aircraft.multi_control_left": "Left", "key.immersive_aircraft.multi_control_right": "Right", "key.immersive_aircraft.multi_control_forward": "Forward", "key.immersive_aircraft.multi_control_backward": "Backward", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Pull Controller", "key.immersive_aircraft.multi_control_push": "Push Controller", "key.immersive_aircraft.multi_use": "Use weapon/mount", "key.immersive_aircraft.fallback_control_left": "Left", "key.immersive_aircraft.fallback_control_right": "Right", "key.immersive_aircraft.fallback_control_forward": "Forward", "key.immersive_aircraft.fallback_control_backward": "Backward", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Pull Controller", "key.immersive_aircraft.fallback_control_push": "Push Controller", "key.immersive_aircraft.fallback_use": "Use weapon/mount", "key.immersive_aircraft.dismount": "Dismount", "key.immersive_aircraft.boost": "Rocket boost", "entity.immersive_aircraft.airship": "Airship", "entity.immersive_aircraft.cargo_airship": "Cargo Airship", "entity.immersive_aircraft.warship": "Warshippy", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON>", "item.immersive_aircraft.engine": "Enginez", "item.immersive_aircraft.sail": "Sail", "item.immersive_aircraft.propeller": "Propeller", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Enhan<PERSON>", "item.immersive_aircraft.eco_engine": "Eco Engine", "item.immersive_aircraft.nether_engine": "Nether Engine", "item.immersive_aircraft.steel_boiler": "Steel Boiler", "item.immersive_aircraft.industrial_gears": "Industrial Gears", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroscope", "item.immersive_aircraft.hull_reinforcement": "Hull Reinforcement", "item.immersive_aircraft.improved_landing_gear": "Improved Landing Gear", "item.immersive_aircraft.rotary_cannon": "Rotary Cannon", "item.immersive_aircraft.bomb_bay": "Bomb Bay", "item.immersive_aircraft.telescope": "Telescope", "item.immersive_aircraft.heavy_crossbow": "Heavy Crossbow", "item.immersive_aircraft.rotary_cannon.description": "Fast-firing cannon wif gunpowder.", "item.immersive_aircraft.bomb_bay.description": "Drops TNT, doez not destwoy blocks but deals heavy damage.", "item.immersive_aircraft.telescope.description": "A bulkier version of da spyglass.", "item.immersive_aircraft.heavy_crossbow.description": "A heavy crossbow wif a powahful punch, wequires arrows.", "item.immersive_aircraft.item.upgrade": "Aircraft Upgrade", "item.immersive_aircraft.item.weapon": "Aircraft Weapon", "item.immersive_aircraft.airship": "Airship", "item.immersive_aircraft.cargo_airship": "Cargo Airship", "item.immersive_aircraft.warship": "Warshippy", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Slow an' fuel hungry but carries a whole storaege.", "item.immersive_aircraft.warship.description": "A flyin' fortress, slow but heavily armed, y'all.", "item.immersive_aircraft.biplane.description": "Dis trusty biplane be fast 'n' rathah reliable. Make shoar yer runway be long 'nuff.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Powah, keep pushin'!", "immersive_aircraft.gyrodyne_target_reached": "Minimul rotor speek reechd, ready fur takeoff!", "immersive_aircraft.invalid_dimension": "Dis aircraft doez not werk in dis dimension.", "immersive_aircraft.out_of_ammo": "Outta ammo!", "immersive_aircraft.repair": "%s%% repaiwed!", "immersive_aircraft.tried_dismount": "Press again to jump out!", "immersive_aircraft.fuel.none": "No fuel!", "immersive_aircraft.fuel.out": "You ran out of fuel!", "immersive_aircraft.fuel.low": "You are low on fuel!", "immersive_aircraft.fat.none": "No food!", "immersive_aircraft.fat.out": "You are too hungry to fly!", "option.immersive_aircraft.general": "General Options", "option.immersive_aircraft.separateCamera": "Use a separate camera in aircraft.", "option.immersive_aircraft.useThirdPersonByDefault": "Defaults to third purrson camera in aircraft.", "option.immersive_aircraft.enableTrails": "Fancy steam trails.", "option.immersive_aircraft.enableAnimatedSails": "Wavey wavey Sails.", "option.immersive_aircraft.renderDistance": "Render distance in blocks.", "option.immersive_aircraft.fuelConsumption": "Fuel burn rate.", "option.immersive_aircraft.windClearWeather": "Base wind effect.", "option.immersive_aircraft.windRainWeather": "Wind at rainfall.", "option.immersive_aircraft.windThunderWeather": "Extra wind at thunder.", "option.immersive_aircraft.repairSpeed": "Repaiw per click.", "option.immersive_aircraft.repairExhaustion": "Player exhaustin' per click, meow.", "option.immersive_aircraft.collisionDamage": "Collision damage.", "option.immersive_aircraft.burnFuelInCreative": "Burn fuel in creative mode.", "option.immersive_aircraft.acceptVanillaFuel": "Accept vanilla fuel.", "option.immersive_aircraft.useCustomKeybindSystem": "Use multi-keybindings, may break certain mods.", "option.immersive_aircraft.showHotbarEngineGauge": "Render da engine gauge over da hotbar.", "option.immersive_aircraft.enableCrashExplosion": "Enable expwosion.", "option.immersive_aircraft.enableCrashBlockDestruction": "Enable destwuctiv expwosion.", "option.immersive_aircraft.enableCrashFire": "Enable fiwy expwosion.", "option.immersive_aircraft.crashExplosionRadius": "Size of crash expwosion.", "option.immersive_aircraft.crashDamage": "Damage da player on a crash.", "option.immersive_aircraft.preventKillThroughCrash": "Will not kill da player on a crash.", "option.immersive_aircraft.healthBarRow": "Offset da health bar of vehicles.", "option.immersive_aircraft.damagePerHealthPoint": "Higher values make aircrap more durable.", "option.immersive_aircraft.weaponsAreDestructive": "Allow some wepons to destroy blokcs, meow.", "option.immersive_aircraft.dropInventory": "Drop teh inventory, instead of saving it wifin teh aircraft item.", "option.immersive_aircraft.dropUpgrades": "Drop teh upgrades an customization, instead of saving it wifin teh aircraft item.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automagically regenerates health every given tick, simulatin' vanilla behavior, y'all!", "option.immersive_aircraft.requireShiftForRepair": "Only repair when holding shift, otherwise just enter teh vehicle.", "immersive_aircraft.tooltip.no_target": "Needs to be assembled on teh floor!", "immersive_aircraft.tooltip.no_space": "Not 'nuff space!", "immersive_aircraft.slot.booster": "Boost rockets", "immersive_aircraft.slot.weapon": "Weapon slot", "immersive_aircraft.slot.boiler": "Fuel slot", "immersive_aircraft.slot.banner": "Banner slot", "immersive_aircraft.slot.dye": "Dye slot", "immersive_aircraft.slot.upgrade": "Upgrade slot", "immersive_aircraft.upgrade.enginespeed": "%s%% engine powah", "immersive_aircraft.upgrade.friction": "%s%% air friction", "immersive_aircraft.upgrade.acceleration": "%s%% takeoff speek", "immersive_aircraft.upgrade.durability": "%s%% durabilitty", "immersive_aircraft.upgrade.fuel": "%s%% fuel requirement", "immersive_aircraft.upgrade.wind": "%s%% wind effect", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizin'", "immersive_aircraft.tooltip.inventory": "Contains %s items."}