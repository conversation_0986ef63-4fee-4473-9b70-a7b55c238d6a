{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Pesawat Terbang Imersif", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON>.", "key.immersive_aircraft.multi_control_forward": "Teruskan", "key.immersive_aircraft.multi_control_backward": "Mundur.", "key.immersive_aircraft.multi_control_up": "Up", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Pengontrol Tarik", "key.immersive_aircraft.multi_control_push": "Pengontrol Dorong", "key.immersive_aircraft.multi_use": "Gunakan senjata/mount", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON>.", "key.immersive_aircraft.fallback_control_forward": "Teruskan", "key.immersive_aircraft.fallback_control_backward": "Mundur.", "key.immersive_aircraft.fallback_control_up": "Up", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Pengontrol Tarik", "key.immersive_aircraft.fallback_control_push": "Pengontrol Dorong", "key.immersive_aircraft.fallback_use": "Gunakan senjata/mount", "key.immersive_aircraft.dismount": "<PERSON><PERSON>", "key.immersive_aircraft.boost": "<PERSON><PERSON><PERSON> roket", "entity.immersive_aircraft.airship": "<PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "<PERSON><PERSON>", "entity.immersive_aircraft.warship": "<PERSON><PERSON>", "entity.immersive_aircraft.biplane": "Biplane", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "Hull", "item.immersive_aircraft.engine": "<PERSON><PERSON>", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "<PERSON><PERSON>-b<PERSON>", "item.immersive_aircraft.boiler": "Ketel", "item.immersive_aircraft.enhanced_propeller": "<PERSON>ng-baling yang <PERSON>", "item.immersive_aircraft.eco_engine": "<PERSON><PERSON>", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON>", "item.immersive_aircraft.steel_boiler": "Ketel Baja", "item.immersive_aircraft.industrial_gears": "Perlengkapan Industri", "item.immersive_aircraft.sturdy_pipes": "Pipa <PERSON>", "item.immersive_aircraft.gyroscope": "Giroskop", "item.immersive_aircraft.hull_reinforcement": "Penguatan <PERSON>", "item.immersive_aircraft.improved_landing_gear": "Perlengkapan Pendaratan yang <PERSON>", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.bomb_bay": "Teluk Bom", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON> yang ditembakkan dengan cepat menggunakan bubuk mesiu.", "item.immersive_aircraft.bomb_bay.description": "Menjatuhkan TNT, tidak menghancurkan blok tetapi memberikan kerusakan besar.", "item.immersive_aircraft.telescope.description": "<PERSON><PERSON><PERSON> teropong yang lebih besar.", "item.immersive_aircraft.heavy_crossbow.description": "Panah yang berat dengan pukulan yang kuat, membut<PERSON>kan anak panah.", "item.immersive_aircraft.item.upgrade": "Peningkatan Pesawat", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship": "<PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "<PERSON><PERSON>", "item.immersive_aircraft.warship": "<PERSON><PERSON>", "item.immersive_aircraft.biplane": "Biplane", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "<PERSON><PERSON> u<PERSON>a mungkin bukan kendaraan tercepat, tetapi sangat mudah untuk bermanuver.", "item.immersive_aircraft.cargo_airship.description": "Slow and fuel hungry but carries an entire storage.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON><PERSON> terbang, lambat namun be<PERSON> le<PERSON>.", "item.immersive_aircraft.biplane.description": "Cepat dan cukup dapat di<PERSON>. Pastikan landasan pacu Anda cukup panjang.", "item.immersive_aircraft.gyrodyne.description": "Siapa yang membutuhkan mesin jika seseorang dapat menggerakkan pesawat dengan kekuatan murni? Berikan dorongan yang baik dan pesawat akan terbang!", "item.immersive_aircraft.quadrocopter.description": "Sebuah mahakarya teknik! 4 rotor yang diikatkan pada bambu. Sempurna untuk bangunan, dan hanya itu.", "immersive_aircraft.gyrodyne_target": "%d%% Kekuatan, terus dorong!", "immersive_aircraft.gyrodyne_target_reached": "Kecepatan rotor minimum tercapai, siap untuk lepas landas!", "immersive_aircraft.invalid_dimension": "Pesawat ini tidak bekerja dalam dimensi ini.", "immersive_aircraft.out_of_ammo": "Ke<PERSON><PERSON> amunisi!", "immersive_aircraft.repair": "%s%% diperbaiki!", "immersive_aircraft.tried_dismount": "Tekan lagi untuk melompat keluar!", "immersive_aircraft.fuel.none": "Tidak ada bahan bakar!", "immersive_aircraft.fuel.out": "Anda kehabi<PERSON> bahan bakar!", "immersive_aircraft.fuel.low": "Anda kehabi<PERSON> bahan bakar!", "immersive_aircraft.fat.none": "Tidak ada makanan!", "immersive_aircraft.fat.out": "Anda terlalu lapar untuk terbang!", "option.immersive_aircraft.general": "Opsi Umum", "option.immersive_aircraft.separateCamera": "Gunakan kamera terpisah di pesawat terbang.", "option.immersive_aircraft.useThirdPersonByDefault": "Default ke kamera orang ketiga di pesawat.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON><PERSON> uap yang mewah.", "option.immersive_aircraft.enableAnimatedSails": "<PERSON>ar yang berombak bergelombang.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON>der jarak dalam blok.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON> p<PERSON> bahan bakar.", "option.immersive_aircraft.windClearWeather": "<PERSON><PERSON><PERSON> angin dasar.", "option.immersive_aircraft.windRainWeather": "<PERSON>in pada saat hujan.", "option.immersive_aircraft.windThunderWeather": "<PERSON><PERSON> ekstra saat guntur.", "option.immersive_aircraft.repairSpeed": "Perbaikan per klik.", "option.immersive_aircraft.repairExhaustion": "<PERSON><PERSON><PERSON><PERSON> pemain per klik.", "option.immersive_aircraft.collisionDamage": "Kerusakan akibat tabrakan.", "option.immersive_aircraft.burnFuelInCreative": "<PERSON><PERSON><PERSON> bahan bakar dalam mode kreatif.", "option.immersive_aircraft.acceptVanillaFuel": "<PERSON><PERSON><PERSON> bahan bakar van<PERSON>.", "option.immersive_aircraft.useCustomKeybindSystem": "Menggunakan multi-keybinding, dapat merusak mod tertentu.", "option.immersive_aircraft.showHotbarEngineGauge": "Render pengukur mesin di atas hotbar.", "option.immersive_aircraft.enableCrashExplosion": "<PERSON><PERSON><PERSON><PERSON>.", "option.immersive_aircraft.enableCrashBlockDestruction": "Mengaktifkan ledakan yang merusak.", "option.immersive_aircraft.enableCrashFire": "Mengaktifkan ledakan yang berapi-api.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON> ledakan tabrakan.", "option.immersive_aircraft.crashDamage": "<PERSON><PERSON><PERSON> pemain saat terjadi tabrakan.", "option.immersive_aircraft.preventKillThroughCrash": "Tidak akan membunuh pemain saat jatuh.", "option.immersive_aircraft.healthBarRow": "Mengimbangi bar k<PERSON><PERSON>an kendaraan.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON> yang lebih tinggi membuat pesawat lebih tahan lama.", "option.immersive_aircraft.weaponsAreDestructive": "<PERSON>zin<PERSON> beberapa senjata untuk menghancurkan blok.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON><PERSON><PERSON> inventaris, alih-alih menyi<PERSON>ya di dalam item pesawat.", "option.immersive_aircraft.dropUpgrades": "<PERSON><PERSON><PERSON><PERSON> peningkatan dan pen<PERSON><PERSON>, alih-alih menyi<PERSON>ya di dalam item pesawat.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "<PERSON><PERSON><PERSON> otomatis mere<PERSON>i kesehatan setiap centang yang diberikan, mensimulasikan perilaku vanilla.", "option.immersive_aircraft.requireShiftForRepair": "<PERSON>ya perbaiki saat memegang shift, jika tidak, masuk saja ke dalam kendaraan.", "immersive_aircraft.tooltip.no_target": "Harus dirakit di atas lantai!", "immersive_aircraft.tooltip.no_space": "Tidak cukup ruang!", "immersive_aircraft.slot.booster": "Meningkatkan roket", "immersive_aircraft.slot.weapon": "<PERSON>lot senjata", "immersive_aircraft.slot.boiler": "Slot bahan bakar", "immersive_aircraft.slot.banner": "Slot spanduk", "immersive_aircraft.slot.dye": "Slot pewarna", "immersive_aircraft.slot.upgrade": "Tingkatkan slot", "immersive_aircraft.upgrade.enginespeed": "%s%% tenaga mesin", "immersive_aircraft.upgrade.friction": "%s%% gesekan udara", "immersive_aircraft.upgrade.acceleration": "%s%% kecepatan lepas landas", "immersive_aircraft.upgrade.durability": "%s%% daya tahan", "immersive_aircraft.upgrade.fuel": "%s%% k<PERSON><PERSON><PERSON><PERSON> bahan bakar", "immersive_aircraft.upgrade.wind": "%s%% efek angin", "immersive_aircraft.upgrade.stabilizer": "%s%% menstabilkan", "immersive_aircraft.tooltip.inventory": "Berisi item %s ."}