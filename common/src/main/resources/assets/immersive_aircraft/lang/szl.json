{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Samoloty do zwiedzania", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON> strona", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Do przodu", "key.immersive_aircraft.multi_control_backward": "Wstecz", "key.immersive_aircraft.multi_control_up": "W górę", "key.immersive_aircraft.multi_control_down": "Down", "key.immersive_aircraft.multi_control_pull": "Kontroler <PERSON>iągnię<PERSON>", "key.immersive_aircraft.multi_control_push": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "key.immersive_aircraft.multi_use": "<PERSON>ż<PERSON>j broni/monta<PERSON>u", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON> strona", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Do przodu", "key.immersive_aircraft.fallback_control_backward": "Wstecz", "key.immersive_aircraft.fallback_control_up": "W górę", "key.immersive_aircraft.fallback_control_down": "Down", "key.immersive_aircraft.fallback_control_pull": "Kontroler <PERSON>iągnię<PERSON>", "key.immersive_aircraft.fallback_control_push": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "key.immersive_aircraft.fallback_use": "<PERSON>ż<PERSON>j broni/monta<PERSON>u", "key.immersive_aircraft.dismount": "Zdemont<PERSON><PERSON> s<PERSON>", "key.immersive_aircraft.boost": "Wzmocnienie rakietowe", "entity.immersive_aircraft.airship": "Sterowiec", "entity.immersive_aircraft.cargo_airship": "Sterowiec towarowy", "entity.immersive_aircraft.warship": "<PERSON><PERSON><PERSON><PERSON> wojenny", "entity.immersive_aircraft.biplane": "Dwupłatowiec", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.hull": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.engine": "Silnik", "item.immersive_aircraft.sail": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.propeller": "Śmigło", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "Ulepszone śmigło", "item.immersive_aircraft.eco_engine": "Silnik Eco", "item.immersive_aircraft.nether_engine": "Silnik sieciowy", "item.immersive_aircraft.steel_boiler": "Kotły stalowe", "item.immersive_aircraft.industrial_gears": "Przekładnie przemysłowe", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON> rury", "item.immersive_aircraft.gyroscope": "Żyroskop", "item.immersive_aircraft.hull_reinforcement": "Wzmocnienie kadłuba", "item.immersive_aircraft.improved_landing_gear": "Ulepszone podwozie", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "item.immersive_aircraft.bomb_bay": "Zatoka bombowa", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "Ciężka kusza", "item.immersive_aircraft.rotary_cannon.description": "Szybkostrzelne działo zasilane prochem strzelniczym.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON>uca TNT, nie niszczy bloków, ale zadaje duże obrażenia.", "item.immersive_aircraft.telescope.description": "Większa wersja okularu szpiegowskiego.", "item.immersive_aircraft.heavy_crossbow.description": "Ciężka kusza z potężnym ciosem, wymaga strzał.", "item.immersive_aircraft.item.upgrade": "Modernizacja <PERSON>u", "item.immersive_aircraft.item.weapon": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship": "Sterowiec", "item.immersive_aircraft.cargo_airship": "Sterowiec towarowy", "item.immersive_aircraft.warship": "<PERSON><PERSON><PERSON><PERSON> wojenny", "item.immersive_aircraft.biplane": "Dwupłatowiec", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopter", "item.immersive_aircraft.airship.description": "Sterowce nie są może najszybszymi pojazdami, ale na pewno łatwo się nimi manewruje.", "item.immersive_aircraft.cargo_airship.description": "Powolny i paliwożerny, ale pomieści cały magazyn.", "item.immersive_aircraft.warship.description": "Lataj<PERSON><PERSON> forteca, pow<PERSON><PERSON>, ale silnie uzbrojona.", "item.immersive_aircraft.biplane.description": "Szybki i raczej niezawodny. Upewnij się, że pas startowy jest wystarczająco długi.", "item.immersive_aircraft.gyrodyne.description": "Po co komu silnik, skoro można napędzać samolot czystą siłą woli? Daj mu dobre pchnięcie i leci!", "item.immersive_aircraft.quadrocopter.description": "Mistrzowskie dzieło inżynierii! 4 wirniki przymocowane do bambusa. Idealne do budowania i tyle.", "immersive_aircraft.gyrodyne_target": "%d%% <PERSON><PERSON>, nie przestawaj naciskać!", "immersive_aircraft.gyrodyne_target_reached": "Osiągnię<PERSON> pręd<PERSON><PERSON> wirnika, gotowy do startu!", "immersive_aircraft.invalid_dimension": "Ten samolot nie działa w tym wymiarze.", "immersive_aircraft.out_of_ammo": "Skończyła się amunicja!", "immersive_aircraft.repair": "%s%% naprawiony!", "immersive_aircraft.tried_dismount": "Naciśnij ponownie, aby wys<PERSON>!", "immersive_aircraft.fuel.none": "Nie ma paliwa!", "immersive_aircraft.fuel.out": "Skończyło się paliwo!", "immersive_aircraft.fuel.low": "Masz mało paliwa!", "immersive_aircraft.fat.none": "Nie ma j<PERSON>!", "immersive_aircraft.fat.out": "Jesteś zbyt głodny, żeby latać!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON>", "option.immersive_aircraft.separateCamera": "Użyj osobnej kamery w samolocie.", "option.immersive_aircraft.useThirdPersonByDefault": "Domyślnie kamera z trzeciej osoby w samolocie.", "option.immersive_aircraft.enableTrails": "Fantazyjne szlaki parowe.", "option.immersive_aircraft.enableAnimatedSails": "Żagle z falami.", "option.immersive_aircraft.renderDistance": "Renderuj odległ<PERSON>ć w blokach.", "option.immersive_aircraft.fuelConsumption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spalania paliwa.", "option.immersive_aircraft.windClearWeather": "Efekt wiatru bazowego.", "option.immersive_aircraft.windRainWeather": "Wiatr podczas opadów.", "option.immersive_aircraft.windThunderWeather": "Dodatkowy wiatr przy grzmotach.", "option.immersive_aircraft.repairSpeed": "Naprawa za kliknięcie.", "option.immersive_aircraft.repairExhaustion": "Wyczerpanie gracza na klik.", "option.immersive_aircraft.collisionDamage": "Uszkodzenie w wyniku kolizji.", "option.immersive_aircraft.burnFuelInCreative": "Spalaj paliwo w trybie kreatywnym.", "option.immersive_aircraft.acceptVanillaFuel": "Przyjmij paliwo waniliowe.", "option.immersive_aircraft.useCustomKeybindSystem": "Użycie wielu powiązań między kluczami może spowodować uszkodzenie niektórych modów.", "option.immersive_aircraft.showHotbarEngineGauge": "Wyrenderuj wskaźnik silnika nad paskiem skrótów.", "option.immersive_aircraft.enableCrashExplosion": "Włącz eksplozję.", "option.immersive_aircraft.enableCrashBlockDestruction": "Włącz niszczycielską eksplozję.", "option.immersive_aircraft.enableCrashFire": "Włącz ognistą eksplozję.", "option.immersive_aircraft.crashExplosionRadius": "Rozmiar eksplozji.", "option.immersive_aircraft.crashDamage": "Zadaj obrażenia graczowi podczas kolizji.", "option.immersive_aircraft.preventKillThroughCrash": "Nie zabije gracza w przypadku awarii.", "option.immersive_aircraft.healthBarRow": "Przesuń pasek zdrowia pojazdów.", "option.immersive_aircraft.damagePerHealthPoint": "W<PERSON>ższe wartości sprawiają, że samolot jest bardziej wytrzymały.", "option.immersive_aircraft.weaponsAreDestructive": "Pozwól niektórym broniom niszczyć bloki.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON><PERSON><PERSON>, zamiast zap<PERSON><PERSON><PERSON> go w elemencie samolotu.", "option.immersive_aircraft.dropUpgrades": "<PERSON><PERSON>ń ulepszenia i personalizację, zamiast zapisywać je w samolocie.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatycznie regeneruje zdrowie co dany tick, symulując zachowanie z wersji vanilla.", "option.immersive_aircraft.requireShiftForRepair": "Naprawiaj tylko trzymając shift, w przeciwnym razie wsiadaj do pojazdu.", "immersive_aircraft.tooltip.no_target": "Wymaga montażu na podłodze!", "immersive_aircraft.tooltip.no_space": "Za mało mi<PERSON>!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.weapon": "Slot na broń", "immersive_aircraft.slot.boiler": "Szczelina na paliwo", "immersive_aircraft.slot.banner": "Slot banerowy", "immersive_aircraft.slot.dye": "Szczelina do farbowania", "immersive_aircraft.slot.upgrade": "Gniazdo uak<PERSON>alnie<PERSON>", "immersive_aircraft.upgrade.enginespeed": "%s%% mocy silnika", "immersive_aircraft.upgrade.friction": "%s%% tarcie powietrza", "immersive_aircraft.upgrade.acceleration": "%s%% prędkość startowa", "immersive_aircraft.upgrade.durability": "%s%% trwałość", "immersive_aircraft.upgrade.fuel": "%s%% zapotrzebowania na paliwo", "immersive_aircraft.upgrade.wind": "%s%% efekt wiatru", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizacji", "immersive_aircraft.tooltip.inventory": "Zawiera elementy %s ."}