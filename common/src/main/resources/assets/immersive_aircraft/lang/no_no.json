{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Immersive Aircraft", "key.immersive_aircraft.multi_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_forward": "Forover", "key.immersive_aircraft.multi_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.multi_control_down": "<PERSON>", "key.immersive_aircraft.multi_control_pull": "Trekk styreenhet", "key.immersive_aircraft.multi_control_push": "Trykk styreenhet", "key.immersive_aircraft.multi_use": "Bruk våpen/feste", "key.immersive_aircraft.fallback_control_left": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_right": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_forward": "Forover", "key.immersive_aircraft.fallback_control_backward": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_up": "<PERSON><PERSON>", "key.immersive_aircraft.fallback_control_down": "<PERSON>", "key.immersive_aircraft.fallback_control_pull": "Trekk styreenhet", "key.immersive_aircraft.fallback_control_push": "Trykk styreenhet", "key.immersive_aircraft.fallback_use": "Bruk våpen/feste", "key.immersive_aircraft.dismount": "Stiga av", "key.immersive_aircraft.boost": "Rakettforsterkning", "entity.immersive_aircraft.airship": "Luftskip", "entity.immersive_aircraft.cargo_airship": "Lasteluftskip", "entity.immersive_aircraft.warship": "Krigsskip", "entity.immersive_aircraft.biplane": "Biplan", "entity.immersive_aircraft.gyrodyne": "Tråkopter", "entity.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.hull": "Flykropp", "item.immersive_aircraft.engine": "Motor", "item.immersive_aircraft.sail": "Seil", "item.immersive_aircraft.propeller": "Propell", "item.immersive_aircraft.boiler": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.enhanced_propeller": "<PERSON><PERSON><PERSON> propell", "item.immersive_aircraft.eco_engine": "Økomotor", "item.immersive_aircraft.nether_engine": "Nethermotor", "item.immersive_aircraft.steel_boiler": "St<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.industrial_gears": "Industriel<PERSON>", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON><PERSON> rø<PERSON>", "item.immersive_aircraft.gyroscope": "Gyroskop", "item.immersive_aircraft.hull_reinforcement": "Skrogforsterkning", "item.immersive_aircraft.improved_landing_gear": "<PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon": "<PERSON><PERSON><PERSON><PERSON> kanon", "item.immersive_aircraft.bomb_bay": "Bombebutikk", "item.immersive_aircraft.telescope": "Teleskop", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON>", "item.immersive_aircraft.rotary_cannon.description": "<PERSON><PERSON><PERSON><PERSON>e kanon som drives med krutt.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> blo<PERSON>, men gjør stor skade.", "item.immersive_aircraft.telescope.description": "En mer omfangsrik versjon av kikkerten.", "item.immersive_aircraft.heavy_crossbow.description": "En tung armbrøst med kraftig slagkraft som krever piler.", "item.immersive_aircraft.item.upgrade": "Luftfartøysoppgradering", "item.immersive_aircraft.item.weapon": "Flyvåpen", "item.immersive_aircraft.airship": "Luftskip", "item.immersive_aircraft.cargo_airship": "Lasteluftskip", "item.immersive_aircraft.warship": "Krigsskip", "item.immersive_aircraft.biplane": "Biplan", "item.immersive_aircraft.gyrodyne": "Tråkopter", "item.immersive_aircraft.quadrocopter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "item.immersive_aircraft.airship.description": "Luftskip er kanskje ikke de raskeste luftfartøyene, men de er lette å manøvrere!", "item.immersive_aircraft.cargo_airship.description": "Den er treg og drivstoffkrevende, men har plass til et helt lager.", "item.immersive_aircraft.warship.description": "En flygende festning, treg, men tungt bevæpnet.", "item.immersive_aircraft.biplane.description": "Rask og ganske pålitelig. <PERSON><PERSON><PERSON> for at rullebanen er lang nok.", "item.immersive_aircraft.gyrodyne.description": "Hvem trenger en motor når du kan drive et luftfartøy med ren muskelkraft? Trå alt hva du orker og fly opp, høyt i det blå!", "item.immersive_aircraft.quadrocopter.description": "Fire propeller festet til en bambusramme – et mesterverk av ingeniørkunst! Perfekt å bygge med, og det er alt.", "immersive_aircraft.gyrodyne_target": "%d%% kraft – fortsett å trå!", "immersive_aircraft.gyrodyne_target_reached": "Klar for start!", "immersive_aircraft.invalid_dimension": "<PERSON>te flyet fungerer ikke i denne dimensjonen.", "immersive_aircraft.out_of_ammo": "Tom for ammunisjon!", "immersive_aircraft.repair": "%s%% reparert!", "immersive_aircraft.tried_dismount": "Trykk en gang til for å hoppe ut!", "immersive_aircraft.fuel.none": "Det finnes ikke noe drivstoff!", "immersive_aircraft.fuel.out": "Drivsto<PERSON>t er gått tomt!", "immersive_aircraft.fuel.low": "Det er knapphet på drivstoff!", "immersive_aircraft.fat.none": "Du er helt utslitt!", "immersive_aircraft.fat.out": "Du orker ikke å trå lenger!", "option.immersive_aircraft.general": "<PERSON><PERSON><PERSON>ill<PERSON>", "option.immersive_aircraft.separateCamera": "Bruk et annet kamera når man er i et luftfartøy", "option.immersive_aircraft.useThirdPersonByDefault": "Bruk tredjepersonsperspektiv i luftfartøy", "option.immersive_aircraft.enableTrails": "Dampspor", "option.immersive_aircraft.enableAnimatedSails": "Blafrende seil", "option.immersive_aircraft.renderDistance": "Skildringsvidde i blokker", "option.immersive_aircraft.fuelConsumption": "Drivstoffforbrenningshastighet", "option.immersive_aircraft.windClearWeather": "Vindpåvirkning i klart vær", "option.immersive_aircraft.windRainWeather": "Vindpåvirkning under ne<PERSON><PERSON><PERSON>r", "option.immersive_aircraft.windThunderWeather": "Vindpåvirkning under <PERSON><PERSON><PERSON><PERSON><PERSON>", "option.immersive_aircraft.repairSpeed": "Reparasjon per klikk.", "option.immersive_aircraft.repairExhaustion": "Spillerutmattelse per klikk.", "option.immersive_aircraft.collisionDamage": "Kollisjonsskade", "option.immersive_aircraft.burnFuelInCreative": "Brenn drivstoff i kreativ modus", "option.immersive_aircraft.acceptVanillaFuel": "Tillat vanilladrivstoff", "option.immersive_aircraft.useCustomKeybindSystem": "Bruk flere tastebindinger (kan ødelegge noen modder)", "option.immersive_aircraft.showHotbarEngineGauge": "<PERSON><PERSON><PERSON>i motormå<PERSON>en over hotbaren.", "option.immersive_aircraft.enableCrashExplosion": "Aktiver eksplosjon.", "option.immersive_aircraft.enableCrashBlockDestruction": "Aktiver destruktiv eksplosjon.", "option.immersive_aircraft.enableCrashFire": "Aktiver brennende eksplosjon.", "option.immersive_aircraft.crashExplosionRadius": "<PERSON><PERSON><PERSON><PERSON> på kollisjonseksplosjonen.", "option.immersive_aircraft.crashDamage": "Skade spilleren ved et krasj.", "option.immersive_aircraft.preventKillThroughCrash": "Dreper ikke spilleren ved et krasj.", "option.immersive_aircraft.healthBarRow": "Forskyv kjøretøyenes helsestang.", "option.immersive_aircraft.damagePerHealthPoint": "<PERSON><PERSON><PERSON><PERSON> verdier gjør flyene mer holdbare.", "option.immersive_aircraft.weaponsAreDestructive": "Tillat at noen våpen kan ødelegge blokker.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON><PERSON>hold<PERSON>, i stedet for å lagre den i flygjenstanden.", "option.immersive_aircraft.dropUpgrades": "<PERSON><PERSON><PERSON> opp<PERSON> og tilpasningen, i stedet for å lagre den i flyelementet.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Regenererer automatisk helse for hvert gitte tikk, og simulerer vaniljeatferd.", "option.immersive_aircraft.requireShiftForRepair": "Reparer bare når du holder skiftet, ellers er det bare å gå inn i kjøretøyet.", "immersive_aircraft.tooltip.no_target": "Må monteres på gulvet!", "immersive_aircraft.tooltip.no_space": "Ikke nok plass!", "immersive_aircraft.slot.booster": "<PERSON><PERSON><PERSON>", "immersive_aircraft.slot.weapon": "Våpenspor", "immersive_aircraft.slot.boiler": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.banner": "Banner", "immersive_aircraft.slot.dye": "<PERSON><PERSON><PERSON><PERSON>", "immersive_aircraft.slot.upgrade": "Oppgradering", "immersive_aircraft.upgrade.enginespeed": "%s%% motoreffekt", "immersive_aircraft.upgrade.friction": "%s%% luftmotstand", "immersive_aircraft.upgrade.acceleration": "%s%% starthastighet", "immersive_aircraft.upgrade.durability": "%s% % holdbarhet", "immersive_aircraft.upgrade.fuel": "%s% % drivstoffbehov", "immersive_aircraft.upgrade.wind": "%s%% vindpåvirkning", "immersive_aircraft.upgrade.stabilizer": "%s%% stabiliserende", "immersive_aircraft.tooltip.inventory": "Inneholder %s elementer."}