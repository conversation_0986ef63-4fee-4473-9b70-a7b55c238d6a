{"itemGroup.immersive_aircraft.immersive_aircraft_tab": "Aviatio Immersiva", "key.immersive_aircraft.multi_control_left": "Sinistrorsum", "key.immersive_aircraft.multi_control_right": "Dextrorsum", "key.immersive_aircraft.multi_control_forward": "Promovendus", "key.immersive_aircraft.multi_control_backward": "Subito", "key.immersive_aircraft.multi_control_up": "Sursum", "key.immersive_aircraft.multi_control_down": "Deorsum", "key.immersive_aircraft.multi_control_pull": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.multi_control_push": "Impellere Controller", "key.immersive_aircraft.multi_use": "Utere telis/montare", "key.immersive_aircraft.fallback_control_left": "Sinistrorsum", "key.immersive_aircraft.fallback_control_right": "Dextrorsum", "key.immersive_aircraft.fallback_control_forward": "Promovendus", "key.immersive_aircraft.fallback_control_backward": "Subito", "key.immersive_aircraft.fallback_control_up": "Sursum", "key.immersive_aircraft.fallback_control_down": "Deorsum", "key.immersive_aircraft.fallback_control_pull": "<PERSON><PERSON><PERSON>", "key.immersive_aircraft.fallback_control_push": "Impellere Controller", "key.immersive_aircraft.fallback_use": "Utere telis/montare", "key.immersive_aircraft.dismount": "Descendere", "key.immersive_aircraft.boost": "Rockets exacerbationem", "entity.immersive_aircraft.airship": "<PERSON><PERSON>", "entity.immersive_aircraft.cargo_airship": "Cargo Aëronave", "entity.immersive_aircraft.warship": "Navi bellica", "entity.immersive_aircraft.biplane": "Biplanum", "entity.immersive_aircraft.gyrodyne": "Gyrodyne", "entity.immersive_aircraft.quadrocopter": "Quadrocopterum", "item.immersive_aircraft.hull": "Corpus", "item.immersive_aircraft.engine": "<PERSON><PERSON><PERSON>", "item.immersive_aircraft.sail": "Velum", "item.immersive_aircraft.propeller": "Propellor", "item.immersive_aircraft.boiler": "Boiler", "item.immersive_aircraft.enhanced_propeller": "Pro<PERSON><PERSON> Emendatus", "item.immersive_aircraft.eco_engine": "Motum Eco", "item.immersive_aircraft.nether_engine": "<PERSON><PERSON>", "item.immersive_aircraft.steel_boiler": "Cineris <PERSON>um", "item.immersive_aircraft.industrial_gears": "Cogitationes Industriales", "item.immersive_aircraft.sturdy_pipes": "<PERSON><PERSON>", "item.immersive_aircraft.gyroscope": "Gyroscopium", "item.immersive_aircraft.hull_reinforcement": "Reinforcamentum Hull", "item.immersive_aircraft.improved_landing_gear": "Libriformia Emendata", "item.immersive_aircraft.rotary_cannon": "Pila Rotatoria", "item.immersive_aircraft.bomb_bay": "Bomba Camara", "item.immersive_aircraft.telescope": "Telescopium", "item.immersive_aircraft.heavy_crossbow": "<PERSON><PERSON> gravis", "item.immersive_aircraft.rotary_cannon.description": "Cannon celeriter Iacula cum sucidia.", "item.immersive_aircraft.bomb_bay.description": "<PERSON><PERSON> mittit, non destruit lapides sed gravia damna facit.", "item.immersive_aircraft.telescope.description": "Amplior versio telescopii.", "item.immersive_aircraft.heavy_crossbow.description": "<PERSON>us gravis cum potentia pugnandi, sagittas requirit.", "item.immersive_aircraft.item.upgrade": "Aeronave Upgrade", "item.immersive_aircraft.item.weapon": "Telum Aeronauticum", "item.immersive_aircraft.airship": "<PERSON><PERSON>", "item.immersive_aircraft.cargo_airship": "Cargo Aëronave", "item.immersive_aircraft.warship": "Navi bellica", "item.immersive_aircraft.biplane": "Biplanum", "item.immersive_aircraft.gyrodyne": "Gyrodyne", "item.immersive_aircraft.quadrocopter": "Quadrocopterum", "item.immersive_aircraft.airship.description": "Airships may not be the fastest vehicle, but they sure are easy to maneuver.", "item.immersive_aircraft.cargo_airship.description": "Lenta et stramenta attenuans sed omnem receptacula portans.", "item.immersive_aircraft.warship.description": "<PERSON><PERSON><PERSON>, lenta sed graviter armata.", "item.immersive_aircraft.biplane.description": "<PERSON><PERSON> et satis fidus. Fac ut recta tua longa satis sit.", "item.immersive_aircraft.gyrodyne.description": "Who needs an engine if one could power the aircraft with pure brawn? Give it a good push and off it flies!", "item.immersive_aircraft.quadrocopter.description": "A masterwork of engineering! 4 rotors strapped to some bamboo. Perfect for building, and that's about it.", "immersive_aircraft.gyrodyne_target": "%d%% Potentia, continue impellens!", "immersive_aircraft.gyrodyne_target_reached": "Minimum velocitas rotorum attingitur, paratus ad avolandum!", "immersive_aircraft.invalid_dimension": "Hic aeroplanum in hac dimensione non operatur.", "immersive_aircraft.out_of_ammo": "Expleta telis!", "immersive_aircraft.repair": "%s%% reparatum!", "immersive_aircraft.tried_dismount": "Premere iterum ut salire!", "immersive_aircraft.fuel.none": "<PERSON>ulla combustibilis!", "immersive_aircraft.fuel.out": "Exhaustus es combustibili!", "immersive_aircraft.fuel.low": "Paulo combustibili caret!", "immersive_aircraft.fat.none": "<PERSON>ulla cibi!", "immersive_aircraft.fat.out": "Nimium es esurit ut voles!", "option.immersive_aircraft.general": "Optiones Generales", "option.immersive_aircraft.separateCamera": "Utere camera separata in aeronave.", "option.immersive_aircraft.useThirdPersonByDefault": "Default ad cameram personae tertiae in aeronave.", "option.immersive_aircraft.enableTrails": "<PERSON><PERSON>.", "option.immersive_aircraft.enableAnimatedSails": "Velasque undulatae.", "option.immersive_aircraft.renderDistance": "<PERSON><PERSON><PERSON> renderizata in blocis.", "option.immersive_aircraft.fuelConsumption": "Rate combustibilis.", "option.immersive_aircraft.windClearWeather": "Efficiens ventorum basium.", "option.immersive_aircraft.windRainWeather": "Ventus ad imbres.", "option.immersive_aircraft.windThunderWeather": "Ventus extra ad tonitrua.", "option.immersive_aircraft.repairSpeed": "Reparare per click.", "option.immersive_aircraft.repairExhaustion": "Exhaustio ludi per click.", "option.immersive_aircraft.collisionDamage": "Damnificatio collisionalis.", "option.immersive_aircraft.burnFuelInCreative": "Utere combustibili in modo creativo.", "option.immersive_aircraft.acceptVanillaFuel": "Accipe combustibile vanilla.", "option.immersive_aircraft.useCustomKeybindSystem": "Utere multi-keybindings, certa modifica frangere potest.", "option.immersive_aircraft.showHotbarEngineGauge": "Fac ut indicem machinae in hotbaria appareat.", "option.immersive_aircraft.enableCrashExplosion": "Activa explosio.", "option.immersive_aircraft.enableCrashBlockDestruction": "Activa explosio destructor.", "option.immersive_aircraft.enableCrashFire": "Activa explosio ignita.", "option.immersive_aircraft.crashExplosionRadius": "Magnitudo explosionis casus.", "option.immersive_aircraft.crashDamage": "Damnum luditori in casu.", "option.immersive_aircraft.preventKillThroughCrash": "Non interficit luditorem in casu.", "option.immersive_aircraft.healthBarRow": "Offset sanitatis tabula vehiculorum.", "option.immersive_aircraft.damagePerHealthPoint": "Maiora valores aeroplanis durabiliora faciunt.", "option.immersive_aircraft.weaponsAreDestructive": "Permitte quasdam telis ad tollenda scuta.", "option.immersive_aircraft.dropInventory": "<PERSON><PERSON> invenit, proservet in rem volatus.", "option.immersive_aircraft.dropUpgrades": "Cadere meliorationes et consuetudines, proservet in rem volatus.", "option.immersive_aircraft.regenerateHealthEveryNTicks": "Automatice sanitatem regenerat omni tick dato, simulating mores vanilan.", "option.immersive_aircraft.requireShiftForRepair": "Solum reparatio dum tenes shift, aliter simpliciter ingredere in vehiculo.", "immersive_aircraft.tooltip.no_target": "In pavimento collocari debet!", "immersive_aircraft.tooltip.no_space": "Non satis spatium!", "immersive_aircraft.slot.booster": "Rockets impulsus", "immersive_aircraft.slot.weapon": "Locum telorum", "immersive_aircraft.slot.boiler": "<PERSON><PERSON>", "immersive_aircraft.slot.banner": "<PERSON><PERSON><PERSON>", "immersive_aircraft.slot.dye": "Locum tincturae", "immersive_aircraft.slot.upgrade": "Slot Upgrade", "immersive_aircraft.upgrade.enginespeed": "%s%% potentiae machinae", "immersive_aircraft.upgrade.friction": "%s%% frictionis aeris", "immersive_aircraft.upgrade.acceleration": "%s%% velocitatis avolandi", "immersive_aircraft.upgrade.durability": "%s%% durabilitatis", "immersive_aircraft.upgrade.fuel": "%s%% exigentiae combustibilis", "immersive_aircraft.upgrade.wind": "%s%% effectus ventorum", "immersive_aircraft.upgrade.stabilizer": "%s%% stabilizing", "immersive_aircraft.tooltip.inventory": "Continet %s articuli."}