{"meta": {"format_version": "4.9", "model_format": "free", "box_uv": false}, "name": "rotary_cannon", "model_identifier": "", "visible_box": [1, 1, 0], "variable_placeholders": "", "variable_placeholder_buttons": [], "timeline_setups": [], "unhandled_root_fields": {}, "resolution": {"width": 32, "height": 32}, "elements": [{"name": "head", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 10, -8.5], "to": [2.5, 15, -6.5], "autouv": 0, "color": 2, "origin": [1.5, 10, -5.5], "uv_offset": [0, 12], "faces": {"north": {"uv": [2, 14, 7, 19], "texture": 0}, "east": {"uv": [0, 14, 2, 19], "texture": 0}, "south": {"uv": [9, 14, 14, 19], "texture": 0}, "west": {"uv": [7, 14, 9, 19], "texture": 0}, "up": {"uv": [7, 14, 2, 12], "texture": 0}, "down": {"uv": [12, 12, 7, 14], "texture": 0}}, "type": "cube", "uuid": "76a7ddb0-5a8d-1b9a-5b56-941025663de1"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3, 0, -3], "to": [3, 2, 3], "autouv": 0, "color": 2, "origin": [0, 0, 0], "uv_offset": [8, 20], "faces": {"north": {"uv": [14, 26, 20, 28], "texture": 0}, "east": {"uv": [8, 26, 14, 28], "texture": 0}, "south": {"uv": [26, 26, 32, 28], "texture": 0}, "west": {"uv": [20, 26, 26, 28], "texture": 0}, "up": {"uv": [20, 26, 14, 20], "texture": 0}, "down": {"uv": [26, 20, 20, 26], "texture": 0}}, "type": "cube", "uuid": "d4e28f99-4945-add5-3f97-4390ad7eb6e8"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 2, -1], "to": [1, 5, 1], "autouv": 0, "color": 2, "mirror_uv": true, "origin": [0, 2, 0], "uv_offset": [0, 7], "faces": {"north": {"uv": [4, 9, 2, 12], "texture": 0}, "east": {"uv": [6, 9, 4, 12], "texture": 0}, "south": {"uv": [8, 9, 6, 12], "texture": 0}, "west": {"uv": [2, 9, 0, 12], "texture": 0}, "up": {"uv": [2, 9, 4, 7], "texture": 0}, "down": {"uv": [4, 7, 6, 9], "texture": 0}}, "type": "cube", "uuid": "fd9dde87-2318-fa1b-aa44-83dd942a7d35"}, {"name": "head", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 9, -0.5], "to": [3.5, 16, 1.5], "autouv": 0, "color": 2, "origin": [1.5, 10, 1.5], "uv_offset": [14, 11], "faces": {"north": {"uv": [16, 13, 23, 20], "texture": 0}, "east": {"uv": [14, 13, 16, 20], "texture": 0}, "south": {"uv": [25, 13, 32, 20], "texture": 0}, "west": {"uv": [23, 13, 25, 20], "texture": 0}, "up": {"uv": [23, 13, 16, 11], "texture": 0}, "down": {"uv": [30, 11, 23, 13], "texture": 0}}, "type": "cube", "uuid": "09de4e93-561b-6a6f-3ad0-1ac35697a1b8"}, {"name": "head", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-3.5, 9, -6.5], "to": [3.5, 16, -2.5], "autouv": 0, "color": 2, "origin": [1.5, 10, -2.5], "uv_offset": [10, 0], "faces": {"north": {"uv": [14, 4, 21, 11], "texture": 0}, "east": {"uv": [10, 4, 14, 11], "texture": 0}, "south": {"uv": [25, 4, 32, 11], "texture": 0}, "west": {"uv": [21, 4, 25, 11], "texture": 0}, "up": {"uv": [21, 4, 14, 0], "texture": 0}, "down": {"uv": [28, 0, 21, 4], "texture": 0}}, "type": "cube", "uuid": "67adb51e-46ce-3cf8-3920-a0e11aa6dfee"}, {"name": "cube", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 5, -4], "to": [1, 7, 1], "autouv": 0, "color": 2, "mirror_uv": true, "origin": [0, 5, -3], "uv_offset": [2, 23], "faces": {"north": {"uv": [9, 28, 7, 30], "texture": 0}, "east": {"uv": [7, 28, 2, 30], "texture": 0}, "south": {"uv": [9, 28, 7, 30], "texture": 0}, "west": {"uv": [7, 28, 2, 30], "texture": 0}, "up": {"uv": [2, 28, 4, 23], "texture": 0}, "down": {"uv": [2, 23, 4, 28], "texture": 0}}, "type": "cube", "uuid": "1b67aeff-5732-9743-53b3-0873ee0a82a3"}, {"name": "cube", "box_uv": true, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-1, 5, -6], "to": [1, 10, -4], "autouv": 0, "color": 2, "origin": [0, 6, -5], "faces": {"north": {"uv": [2, 2, 4, 7], "texture": 0}, "east": {"uv": [0, 2, 2, 7], "texture": 0}, "south": {"uv": [6, 2, 8, 7], "texture": 0}, "west": {"uv": [4, 2, 6, 7], "texture": 0}, "up": {"uv": [4, 2, 2, 0], "texture": 0}, "down": {"uv": [6, 0, 4, 2], "texture": 0}}, "type": "cube", "uuid": "20a01197-3129-0aa5-d716-a54985fc88e7"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 10, -2.5], "to": [2.5, 12, 10.5], "autouv": 0, "color": 2, "origin": [1.5, 10, 1.5], "faces": {"north": {"uv": [0, 30, 2, 32], "texture": 0}, "east": {"uv": [2, 30, 15, 32], "texture": 0}, "south": {"uv": [15, 30, 17, 32], "texture": 0}, "west": {"uv": [2, 30, 15, 32], "texture": 0}, "up": {"uv": [2, 32, 0, 19], "texture": 0}, "down": {"uv": [2, 19, 0, 32], "texture": 0}}, "type": "cube", "uuid": "a19f13e8-2b7b-3977-31b6-ad2241f04d41"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 10, -2.5], "to": [-0.5, 12, 10.5], "autouv": 0, "color": 2, "origin": [-1.5, 10, 1.5], "faces": {"north": {"uv": [0, 30, 2, 32], "texture": 0}, "east": {"uv": [2, 30, 15, 32], "texture": 0}, "south": {"uv": [15, 30, 17, 32], "texture": 0}, "west": {"uv": [2, 30, 15, 32], "texture": 0}, "up": {"uv": [2, 32, 0, 19], "texture": 0}, "down": {"uv": [2, 19, 0, 32], "texture": 0}}, "type": "cube", "uuid": "e824bb94-83e0-2544-270e-e7c422455262"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [-2.5, 13, -2.5], "to": [-0.5, 15, 10.5], "autouv": 0, "color": 2, "origin": [-1.5, 13, 1.5], "faces": {"north": {"uv": [0, 30, 2, 32], "texture": 0}, "east": {"uv": [2, 30, 15, 32], "texture": 0}, "south": {"uv": [15, 30, 17, 32], "texture": 0}, "west": {"uv": [15, 32, 2, 30], "texture": 0}, "up": {"uv": [2, 32, 0, 19], "texture": 0}, "down": {"uv": [2, 19, 0, 32], "texture": 0}}, "type": "cube", "uuid": "e5d44811-172c-bb05-8bfe-1213ce97c17a"}, {"name": "head", "box_uv": false, "rescale": false, "locked": false, "render_order": "default", "allow_mirror_modeling": true, "from": [0.5, 13, -2.5], "to": [2.5, 15, 10.5], "autouv": 0, "color": 2, "origin": [1.5, 13, 1.5], "faces": {"north": {"uv": [0, 30, 2, 32], "texture": 0}, "east": {"uv": [2, 30, 15, 32], "texture": 0}, "south": {"uv": [15, 30, 17, 32], "texture": 0}, "west": {"uv": [15, 32, 2, 30], "texture": 0}, "up": {"uv": [2, 32, 0, 19], "texture": 0}, "down": {"uv": [2, 19, 0, 32], "texture": 0}}, "type": "cube", "uuid": "1841d14f-65b4-b4f9-7b57-8c22cc1ee902"}], "outliner": ["d4e28f99-4945-add5-3f97-4390ad7eb6e8", "fd9dde87-2318-fa1b-aa44-83dd942a7d35", "20a01197-3129-0aa5-d716-a54985fc88e7", "1b67aeff-5732-9743-53b3-0873ee0a82a3", {"name": "yaw", "origin": [0, 9, -5.5], "color": 0, "uuid": "8153e5e2-0bb9-f54f-1d28-b49c476aef59", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "pitch", "origin": [0, 12, -5], "color": 0, "uuid": "af039420-d48e-6ad1-0cde-b07c8332b112", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": [{"name": "roll", "origin": [0, 12.5, -5.5], "color": 0, "uuid": "6b4528c8-4f78-e0cb-e80a-da359d9e0a6c", "export": true, "mirror_uv": false, "isOpen": true, "locked": false, "visibility": true, "autouv": 0, "children": ["09de4e93-561b-6a6f-3ad0-1ac35697a1b8", "76a7ddb0-5a8d-1b9a-5b56-941025663de1", "e824bb94-83e0-2544-270e-e7c422455262", "e5d44811-172c-bb05-8bfe-1213ce97c17a", "a19f13e8-2b7b-3977-31b6-ad2241f04d41", "1841d14f-65b4-b4f9-7b57-8c22cc1ee902", "67adb51e-46ce-3cf8-3920-a0e11aa6dfee"]}]}]}], "textures": [{"path": "/DATA/myprograms_Java/ImmersiveAircraft/common/src/main/resources/assets/immersive_aircraft/textures/entity/rotary_cannon.png", "name": "rotary_cannon.png", "folder": "block", "namespace": "", "id": "0", "width": 32, "height": 32, "uv_width": 32, "uv_height": 32, "particle": false, "layers_enabled": false, "sync_to_project": "", "render_mode": "default", "render_sides": "auto", "frame_time": 1, "frame_order_type": "loop", "frame_order": "", "frame_interpolate": false, "visible": true, "internal": true, "saved": true, "uuid": "5f1b21e1-ad3e-903d-de03-e88ab541b850", "relative_path": "../../textures/entity/rotary_cannon.png", "source": "data:image/png;base64,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"}], "animations": [{"uuid": "b3bd329c-d4b1-37b1-8948-1c439dcaa6b1", "name": "animation", "loop": "loop", "override": false, "length": 0, "snapping": 24, "selected": true, "anim_time_update": "", "blend_weight": "", "start_delay": "", "loop_delay": "", "animators": {"8153e5e2-0bb9-f54f-1d28-b49c476aef59": {"name": "yaw", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "-variable.yaw", "z": "0"}], "uuid": "48e2842e-b9bf-fed8-a813-56c669b10d74", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "af039420-d48e-6ad1-0cde-b07c8332b112": {"name": "pitch", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "variable.pitch", "y": "0", "z": "0"}], "uuid": "dd6e3e4a-6f25-553e-269f-afef385911d0", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}, "6b4528c8-4f78-e0cb-e80a-da359d9e0a6c": {"name": "roll", "type": "bone", "keyframes": [{"channel": "rotation", "data_points": [{"x": "0", "y": "0", "z": "-variable.roll"}], "uuid": "2d504aa3-6ed5-274e-2529-b53ee7da36b7", "time": 0, "color": -1, "interpolation": "linear", "bezier_linked": true, "bezier_left_time": [-0.1, -0.1, -0.1], "bezier_left_value": [0, 0, 0], "bezier_right_time": [0.1, 0.1, 0.1], "bezier_right_value": [0, 0, 0]}]}}}]}