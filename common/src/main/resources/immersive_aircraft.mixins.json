{"required": true, "minVersion": "0.7", "package": "immersive_aircraft.mixin", "compatibilityLevel": "JAVA_17", "mixins": ["PlayerEntityMixin", "ProjectileUtilMixin", "ServerPlayerEntityMixin"], "injectors": {"defaultRequire": 1}, "client": ["client.AbstractClientPlayerMixin", "client.CameraMixin", "client.ClientPlayerEntityMixin", "client.ClientPlayerInteractionManagerMixin", "client.ClientPlayNetworkHandlerMixin", "client.EntityRenderDispatcherMixin", "client.GameRendererMixin", "client.GuiMixin", "client.KeyMappingAccessorMixin", "client.KeyMappingMixin", "client.LivingEntityRendererMixin", "client.PlayerEntityRendererMixin"]}