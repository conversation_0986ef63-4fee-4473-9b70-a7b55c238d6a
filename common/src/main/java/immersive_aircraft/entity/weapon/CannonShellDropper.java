package immersive_aircraft.entity.weapon;

import immersive_aircraft.entity.VehicleEntity;
import immersive_aircraft.entity.InventoryVehicleEntity;
import immersive_aircraft.entity.misc.WeaponMount;
import immersive_aircraft.cobalt.network.NetworkHandler;
import immersive_aircraft.network.c2s.FireMessage;
import net.minecraft.core.registries.BuiltInRegistries;
import net.minecraft.nbt.CompoundTag;
import net.minecraft.sounds.SoundEvents;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.EntityType;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.entity.projectile.AbstractArrow;
import net.minecraft.world.entity.projectile.Arrow;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix3f;
import org.joml.Vector3f;
import org.joml.Vector4f;

import java.util.Map;

public class CannonShellDropper extends Weapon {
    private static final float MAX_COOLDOWN = 2.0f;
    private float cooldown = 0.0f;
    private int ammo = 0;
    private ItemStack ammoStack;
    private boolean useFirstPosition = true; // Alternates between two drop positions

    // Create Big Cannon shell items that this weapon can use
    // These IDs should work with Create Big Cannons for 1.20.1
    private static final Map<String, Integer> CANNON_SHELL_AMMUNITION = Map.of(
            "createbigcannons:ap_shell", 1,
            "createbigcannons:he_shell", 1,
            "createbigcannons:shrapnel_shell", 1,
            "createbigcannons:smoke_shell", 1,
            "createbigcannons:gas_shell", 1,
            "createbigcannons:fluid_shell", 1,
            "canonnukes:nuke_shell", 1,
            // Fallback items if Create Big Cannons isn't available
            "minecraft:tnt", 1,
            "minecraft:fire_charge", 1
    );

    public CannonShellDropper(VehicleEntity entity, ItemStack stack, WeaponMount mount, int slot) {
        super(entity, stack, mount, slot);
    }

    @Override
    public void tick() {
        cooldown -= 1.0f / 20.0f;
    }

    @Override
    public void fire(Vector3f direction) {
        if (spentAmmo(CANNON_SHELL_AMMUNITION, 1)) {
            dropCannonShell(direction);
            // Alternate between drop positions for next shell
            useFirstPosition = !useFirstPosition;
        } else {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
        }
    }

    @Override
    public void clientFire(int index) {
        if (cooldown <= 0.0f) {
            cooldown = MAX_COOLDOWN;
            NetworkHandler.sendToServer(new FireMessage(getSlot(), index, getDirection()));
        }

    }
    private void dropCannonShell(Vector3f direction) {
        // Calculate the drop position
        Vector4f position = getDropOffset();
        VehicleEntity entity = getEntity();
        position.mul(getMount().transform());
        position.mul(entity.getVehicleTransform());

        // Try to spawn Create Big Cannon shell entity
        if (ammoStack != null && !ammoStack.isEmpty()) {
            Entity shellEntity = createCannonShellEntity(entity, ammoStack, position, direction);

            if (shellEntity != null) {
                entity.level().addFreshEntity(shellEntity);
            }
        }

        // Play sound
        getEntity().playSound(SoundEvents.DISPENSER_DISPENSE, 1.0f, 0.8f + 0.4f * entity.level().random.nextFloat());
    }





    private Entity createCannonShellEntity(VehicleEntity vehicle, ItemStack ammoStack, Vector4f position, Vector3f direction) {
        String itemId = BuiltInRegistries.ITEM.getKey(ammoStack.getItem()).toString();

        // Try to create Create Big Cannon shell entities
        Entity shellEntity = null;

        switch (itemId) {
            case "createbigcannons:ap_shell":
                shellEntity = createShellEntity("createbigcannons:ap_shell", vehicle, position, direction);
                break;
            case "createbigcannons:he_shell":
                shellEntity = createShellEntity("createbigcannons:he_shell", vehicle, position, direction);
                break;
            case "createbigcannons:shrapnel_shell":
                shellEntity = createShellEntity("createbigcannons:shrapnel_shell", vehicle, position, direction);
                break;
            case "createbigcannons:smoke_shell":
                shellEntity = createShellEntity("createbigcannons:smoke_shell", vehicle, position, direction);
                break;
            case "createbigcannons:gas_shell":
                shellEntity = createShellEntity("createbigcannons:gas_shell", vehicle, position, direction);
                break;
            case "createbigcannons:fluid_shell":
                shellEntity = createShellEntity("createbigcannons:fluid_shell", vehicle, position, direction);
                break;
            case "canonnukes:nuke_shell":
                shellEntity = createShellEntity("canonnukes:nuke_shell_projectile", vehicle, position, direction);
                break;
//            case "createbigcannons:nuclear_shell":
//                shellEntity = createShellEntity("createbigcannons:nuclear_shell", vehicle, position, direction);
//                break;
            default:
                // Fallback: create a simple arrow projectile for vanilla items
                Arrow arrow = new Arrow(vehicle.level(), position.x(), position.y(), position.z());
                arrow.setOwner(vehicle.getControllingPassenger());
                // Inherit aircraft velocity exactly
                arrow.setDeltaMovement(vehicle.getDeltaMovement());
                shellEntity = arrow;
                break;
        }

        return shellEntity;
    }

    private Entity createShellEntity(String entityId, VehicleEntity vehicle, Vector4f position, Vector3f direction) {
        try {
            ResourceLocation entityLocation = ResourceLocation.tryParse(entityId);
            if (entityLocation != null && BuiltInRegistries.ENTITY_TYPE.containsKey(entityLocation)) {
                EntityType<?> entityType = BuiltInRegistries.ENTITY_TYPE.get(entityLocation);
                Entity shell = entityType.create(vehicle.level());
//                System.out.println(shell.getUUID());
                if (shell != null) {
                    shell.setPos(position.x(), position.y()-2, position.z());

                    // Set velocity - inherit aircraft velocity exactly
                    Vec3 aircraftVelocity = vehicle.getSpeedVector();
                    shell.setDeltaMovement(aircraftVelocity);
                    // Set owner if it's a projectile
                    if (shell instanceof AbstractArrow arrow && vehicle.getControllingPassenger() != null) {
                        arrow.setOwner(vehicle.getControllingPassenger());
                    }

                    // Set up shell properties using NBT data
                    setupShellProperties(shell, ammoStack);

                    return shell;
                }
            }
        } catch (Exception e) {
            // If Create Big Cannons entity creation fails, return null to use fallback
        }

        return null;
    }

    private void setupShellProperties(Entity shell, ItemStack ammoStack) {

        try {
            CompoundTag shellNBT = new CompoundTag();
            shell.saveWithoutId(shellNBT);
            CompoundTag itemNBT = ammoStack.getTag();

            // Copy fuse data from the item to the shell entity

            if (itemNBT != null) {
                if (itemNBT.contains("BlockEntityTag") && itemNBT.get("BlockEntityTag") instanceof CompoundTag) {
                    if (((CompoundTag) itemNBT.get("BlockEntityTag")).contains("Fuze")) {

                        shellNBT.put("Fuze", ((CompoundTag) itemNBT.get("BlockEntityTag")).get("Fuze"));
                    }
                }
                shellNBT.putBoolean("HasBeenShot", true);

            }
            shell.load(shellNBT);
        } catch (Exception e) {
            // If NBT setup fails, shell will still work with default properties
        }
    }



    private Vector4f getDropOffset() {
        return new Vector4f(0.0f, -1.0f, 0.0f, 1.0f);
    }

    private Vector3f getDirection() {
        Vector3f direction = new Vector3f(0, -1.0f, 0);
        direction.mul(new Matrix3f(getMount().transform()));
        direction.mul(getEntity().getVehicleNormalTransform());
        return direction;
    }

    protected boolean spentAmmo(Map<String, Integer> ammunition, int amount) {
        if (ammo < amount && getEntity() instanceof InventoryVehicleEntity vehicle) {
            for (int i = 0; i < vehicle.getInventory().getContainerSize(); i++) {
                ItemStack stack = vehicle.getInventory().getItem(i);
                String key = BuiltInRegistries.ITEM.getKey(stack.getItem()).toString();
                if (ammunition.containsKey(key)) {
                    ammoStack = stack.copy();
                    ammoStack.setCount(1); // Only drop one shell at a time

                    if (!getEntity().isPilotCreative()) {
                        ammo += ammunition.get(key);
                        stack.shrink(1);
                    }
                    break;
                }
            }
        }

        if (getEntity().isPilotCreative()) {
            // In creative mode, create a default shell if no ammo stack exists
            return ammoStack != null && !ammoStack.isEmpty();
        }

        if (ammo <= 0) {
            if (getEntity().getControllingPassenger() instanceof Player player) {
                player.displayClientMessage(Component.translatable("immersive_aircraft.out_of_ammo"), true);
            }
            return false;
        }

        ammo -= amount;
        return true;
    }
}
